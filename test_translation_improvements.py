#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻译脚本的改进功能
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from translate_corpus_en import CorpusEnTranslator

def test_improved_features():
    """测试改进的功能"""
    translator = CorpusEnTranslator()
    
    print("=" * 60)
    print("测试翻译脚本改进功能")
    print("=" * 60)
    
    # 测试1: JSON验证
    print("\n1. 测试JSON验证功能:")
    test_cases = [
        ('{"name": "<PERSON>", "age": 30}', True, "有效JSON"),
        ('{"name": "<PERSON>", "age": 30', False, "无效JSON - 缺少闭合括号"),
        ('{"name": "张三", "age": 30}', False, "包含中文的JSON"),
        ('{"星座": "白羊座", "运势": "很好"}', False, "全中文JSON"),
    ]
    
    for test_json, expected, description in test_cases:
        if "中文" in description:
            is_valid, error = translator.validate_translation_result(test_json, 'new_content')
        else:
            is_valid, error = translator.validate_json(test_json)
        
        status = "✓" if is_valid == expected else "✗"
        print(f"  {status} {description}: {is_valid} {'(错误: ' + error + ')' if error else ''}")
    
    # 测试2: Markdown验证
    print("\n2. 测试Markdown验证功能:")
    markdown_cases = [
        ("# Hello World\n\nThis is **bold** text.", True, "有效英文Markdown"),
        ("# 你好世界\n\n这是**粗体**文本。", False, "包含中文的Markdown"),
        ("## Astrology Chart\n\n- Mercury in Aries\n- Venus in Taurus", True, "占星术语Markdown"),
    ]
    
    for test_md, expected, description in markdown_cases:
        is_valid, error = translator.validate_translation_result(test_md, 'chart_content_markdown')
        status = "✓" if is_valid == expected else "✗"
        print(f"  {status} {description}: {is_valid} {'(错误: ' + error + ')' if error else ''}")
    
    # 测试3: HTML验证
    print("\n3. 测试HTML验证功能:")
    html_cases = [
        ("<p>Hello <strong>World</strong></p>", True, "有效英文HTML"),
        ("<p>你好 <strong>世界</strong></p>", False, "包含中文的HTML"),
        ("<div><h1>Astrology Reading</h1><p>Your chart shows...</p></div>", True, "复杂HTML结构"),
    ]
    
    for test_html, expected, description in html_cases:
        is_valid, error = translator.validate_translation_result(test_html, 'content')
        status = "✓" if is_valid == expected else "✗"
        print(f"  {status} {description}: {is_valid} {'(错误: ' + error + ')' if error else ''}")
    
    # 测试4: 中文检测
    print("\n4. 测试中文检测功能:")
    chinese_cases = [
        ("Hello World", False, "纯英文"),
        ("你好世界", True, "纯中文"),
        ("Hello 世界", True, "中英混合"),
        ("占星学 Astrology", True, "中文术语"),
        ("", False, "空字符串"),
    ]
    
    for text, expected, description in chinese_cases:
        has_chinese = translator.has_chinese(text)
        status = "✓" if has_chinese == expected else "✗"
        print(f"  {status} {description}: {has_chinese}")
    
    # 测试5: 错误日志功能
    print("\n5. 测试错误日志功能:")
    try:
        translator.log_error_to_file(
            record_id=999,
            field_name="test_field",
            error_msg="测试错误 - JSON格式不正确",
            original_text='{"name": "张三", "age": 30}',
            translated_text='{"name": "Zhang San", "age": 30'  # 故意的格式错误
        )
        print("  ✓ 错误日志写入成功")
        
        # 检查文件是否存在
        if os.path.exists(translator.error_log_file):
            print(f"  ✓ 错误日志文件已创建: {translator.error_log_file}")
        else:
            print("  ✗ 错误日志文件未找到")
            
    except Exception as e:
        print(f"  ✗ 错误日志功能测试失败: {e}")
    
    # 测试6: 清理翻译结果功能
    print("\n6. 测试翻译结果清理功能:")
    clean_cases = [
        ('```json\n{"name": "John"}\n```', '{"name": "John"}', 'new_content', "清理JSON代码块"),
        ('```markdown\n# Hello\n```', '# Hello', 'chart_content_markdown', "清理Markdown代码块"),
        ('{"name": "John"}', '{"name": "John"}', 'new_content', "无需清理的JSON"),
    ]
    
    for original, expected, field_name, description in clean_cases:
        cleaned = translator.clean_translation_result(original, field_name)
        status = "✓" if cleaned == expected else "✗"
        print(f"  {status} {description}: '{cleaned}'")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    test_improved_features()
