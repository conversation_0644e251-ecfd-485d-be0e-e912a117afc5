#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 corpus_en 表中指定字段是否为有效的JSON格式，如果不是则设置为null
"""

import mysql.connector
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('validate_json_fields.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JsonFieldValidator:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        # 需要检查的JSON字段列表
        self.json_fields = [
            'new_content',
            'transit_chart_content',
            'combination_chart_content',
            'thirdprogressed_chart_content',
            'secondarylimit_chart_content',
            'lunarreturn_chart_content',
            'solarreturn_chart_content',
            'solararc_chart_content',
            'developed_chart_content',
            'smalllimit_chart_content',
            'nataltwelvepointer_chart_content',
            'natalthirteenpointer_chart_content',
            'current_chart_content',
            'chart_content_markdown',
            'comparision_a_chart_content',
            'comparision_b_chart_content',
            'compositeThirprogr_chart_content',
            'compositesecondary_chart_content',
            'marks_a_chart_content',
            'marks_b_chart_content',
            'marksthirprogr_a_chart_content',
            'marksthirprogr_b_chart_content',
            'markssecprogr_a_chart_content',
            'markssecprogr_b_chart_content',
            'timesmidpoint_chart_content',
            'timesmidpointthirprogr_chart_content',
            'timesmidpointsecprogr_chart_content'
        ]
        
        # 数据库连接
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def is_valid_json(self, text: str) -> bool:
        """检查文本是否为有效的JSON格式"""
        if not text or text.strip() == '':
            return True  # 空值或空字符串认为是有效的
        
        try:
            json.loads(text)
            return True
        except (json.JSONDecodeError, TypeError):
            return False
    
    def get_records_with_invalid_json(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取包含无效JSON的记录"""
        try:
            # 构建查询，获取所有记录的JSON字段
            fields_str = ', '.join(['id'] + self.json_fields)
            query = f"SELECT {fields_str} FROM corpus_en ORDER BY id"
            
            if limit:
                query += f" LIMIT {limit}"
            
            self.cursor.execute(query)
            records = self.cursor.fetchall()
            
            invalid_records = []
            
            for record in records:
                record_id = record['id']
                invalid_fields = []
                
                for field in self.json_fields:
                    field_value = record.get(field)
                    if field_value and not self.is_valid_json(field_value):
                        invalid_fields.append(field)
                
                if invalid_fields:
                    invalid_records.append({
                        'id': record_id,
                        'invalid_fields': invalid_fields,
                        'record': record
                    })
            
            logger.info(f"找到 {len(invalid_records)} 条包含无效JSON的记录")
            return invalid_records
            
        except Exception as e:
            logger.error(f"查询无效JSON记录失败: {e}")
            return []
    
    def preview_invalid_records(self, limit: int = 10):
        """预览包含无效JSON的记录"""
        try:
            invalid_records = self.get_records_with_invalid_json(limit)
            
            if not invalid_records:
                logger.info("没有找到包含无效JSON的记录")
                return
            
            logger.info(f"预览前 {len(invalid_records)} 条包含无效JSON的记录：")
            
            for record_info in invalid_records:
                record_id = record_info['id']
                invalid_fields = record_info['invalid_fields']
                
                logger.info(f"ID: {record_id}, 无效JSON字段: {', '.join(invalid_fields)}")
                
                # 显示每个无效字段的前100个字符
                for field in invalid_fields[:3]:  # 只显示前3个字段的内容
                    field_value = record_info['record'].get(field, '')
                    preview_text = str(field_value)[:100] + ('...' if len(str(field_value)) > 100 else '')
                    logger.info(f"  {field}: {preview_text}")
                
                if len(invalid_fields) > 3:
                    logger.info(f"  ... 还有 {len(invalid_fields) - 3} 个无效字段")
                
                logger.info("-" * 50)
                
        except Exception as e:
            logger.error(f"预览无效记录失败: {e}")
    
    def fix_invalid_json_fields(self, limit: int = None, dry_run: bool = True):
        """修复无效的JSON字段，将其设置为null"""
        try:
            invalid_records = self.get_records_with_invalid_json(limit)
            
            if not invalid_records:
                logger.info("没有需要修复的记录")
                return
            
            logger.info(f"找到 {len(invalid_records)} 条需要修复的记录")
            
            if dry_run:
                logger.info("=== 预演模式 (不会实际修改数据) ===")
            else:
                logger.info("=== 实际修复模式 ===")
            
            success_count = 0
            failed_count = 0
            total_fixed_fields = 0
            
            for record_info in invalid_records:
                record_id = record_info['id']
                invalid_fields = record_info['invalid_fields']
                
                logger.info(f"处理记录 ID: {record_id}, 需要修复的字段: {', '.join(invalid_fields)}")
                
                if not dry_run:
                    try:
                        # 构建UPDATE语句，将无效JSON字段设置为NULL
                        update_parts = [f"{field} = NULL" for field in invalid_fields]
                        query = f"""
                        UPDATE corpus_en 
                        SET {', '.join(update_parts)}
                        WHERE id = %s
                        """
                        
                        self.cursor.execute(query, (record_id,))
                        self.connection.commit()
                        
                        logger.info(f"记录 {record_id} 修复成功，已将 {len(invalid_fields)} 个字段设置为NULL")
                        success_count += 1
                        total_fixed_fields += len(invalid_fields)
                        
                    except Exception as e:
                        logger.error(f"修复记录 {record_id} 失败: {e}")
                        failed_count += 1
                else:
                    logger.info(f"[预演] 将修复记录 {record_id} 的 {len(invalid_fields)} 个字段")
                    success_count += 1
                    total_fixed_fields += len(invalid_fields)
            
            if dry_run:
                logger.info(f"预演完成！将修复 {success_count} 条记录，共 {total_fixed_fields} 个字段")
            else:
                logger.info(f"修复完成！成功: {success_count}, 失败: {failed_count}, 共修复 {total_fixed_fields} 个字段")
            
        except Exception as e:
            logger.error(f"修复无效JSON字段失败: {e}")
    
    def validate_specific_record(self, record_id: int):
        """验证特定记录的JSON字段"""
        try:
            fields_str = ', '.join(['id'] + self.json_fields)
            query = f"SELECT {fields_str} FROM corpus_en WHERE id = %s"
            
            self.cursor.execute(query, (record_id,))
            record = self.cursor.fetchone()
            
            if not record:
                logger.error(f"未找到ID为 {record_id} 的记录")
                return
            
            logger.info(f"验证记录 ID: {record_id}")
            
            valid_fields = []
            invalid_fields = []
            
            for field in self.json_fields:
                field_value = record.get(field)
                if field_value:
                    if self.is_valid_json(field_value):
                        valid_fields.append(field)
                    else:
                        invalid_fields.append(field)
                        # 显示无效内容的前200个字符
                        preview_text = str(field_value)[:200] + ('...' if len(str(field_value)) > 200 else '')
                        logger.info(f"  无效JSON字段 {field}: {preview_text}")
            
            logger.info(f"有效JSON字段 ({len(valid_fields)}): {', '.join(valid_fields) if valid_fields else '无'}")
            logger.info(f"无效JSON字段 ({len(invalid_fields)}): {', '.join(invalid_fields) if invalid_fields else '无'}")
            
        except Exception as e:
            logger.error(f"验证记录失败: {e}")
    
    def get_statistics(self):
        """获取JSON字段的统计信息"""
        try:
            logger.info("正在统计JSON字段情况...")
            
            # 获取总记录数
            self.cursor.execute("SELECT COUNT(*) as total FROM corpus_en")
            total_records = self.cursor.fetchone()['total']
            
            logger.info(f"总记录数: {total_records}")
            
            # 统计每个字段的情况
            field_stats = {}
            
            for field in self.json_fields:
                # 非空记录数
                self.cursor.execute(f"SELECT COUNT(*) as count FROM corpus_en WHERE {field} IS NOT NULL AND {field} != ''")
                non_empty_count = self.cursor.fetchone()['count']
                
                field_stats[field] = {
                    'non_empty': non_empty_count,
                    'valid_json': 0,
                    'invalid_json': 0
                }
            
            # 检查JSON有效性（分批处理以避免内存问题）
            batch_size = 1000
            offset = 0
            
            while offset < total_records:
                fields_str = ', '.join(['id'] + self.json_fields)
                query = f"SELECT {fields_str} FROM corpus_en ORDER BY id LIMIT {batch_size} OFFSET {offset}"
                
                self.cursor.execute(query)
                records = self.cursor.fetchall()
                
                if not records:
                    break
                
                for record in records:
                    for field in self.json_fields:
                        field_value = record.get(field)
                        if field_value:
                            if self.is_valid_json(field_value):
                                field_stats[field]['valid_json'] += 1
                            else:
                                field_stats[field]['invalid_json'] += 1
                
                offset += batch_size
                logger.info(f"已处理 {min(offset, total_records)}/{total_records} 条记录")
            
            # 输出统计结果
            logger.info("\n=== JSON字段统计结果 ===")
            for field, stats in field_stats.items():
                logger.info(f"{field}:")
                logger.info(f"  非空记录: {stats['non_empty']}")
                logger.info(f"  有效JSON: {stats['valid_json']}")
                logger.info(f"  无效JSON: {stats['invalid_json']}")
                if stats['non_empty'] > 0:
                    valid_rate = (stats['valid_json'] / stats['non_empty']) * 100
                    logger.info(f"  有效率: {valid_rate:.2f}%")
                logger.info("-" * 30)
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("开始验证 corpus_en 表中JSON字段的有效性")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)
    
    validator = JsonFieldValidator()
    
    if not validator.connect_database():
        return
    
    try:
        print("\n请选择操作:")
        print("1. 预览包含无效JSON的记录")
        print("2. 获取JSON字段统计信息")
        print("3. 验证特定记录")
        print("4. 预演修复无效JSON字段")
        print("5. 实际修复无效JSON字段")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '1':
            limit = input("输入预览记录数限制 (默认10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            validator.preview_invalid_records(limit)
            
        elif choice == '2':
            validator.get_statistics()
            
        elif choice == '3':
            record_id = input("请输入记录ID: ").strip()
            if record_id.isdigit():
                validator.validate_specific_record(int(record_id))
            else:
                logger.error("无效的记录ID")
                
        elif choice == '4':
            limit = input("输入处理记录数限制 (留空处理所有): ").strip()
            limit = int(limit) if limit.isdigit() else None
            validator.fix_invalid_json_fields(limit, dry_run=True)
            
        elif choice == '5':
            limit = input("输入处理记录数限制 (留空处理所有): ").strip()
            limit = int(limit) if limit.isdigit() else None
            
            print(f"\n⚠️  警告：这将实际修改数据库，将无效JSON字段设置为NULL")
            confirm = input("确认继续？(y/n): ").strip().lower()
            
            if confirm in ['y', 'yes', '是']:
                validator.fix_invalid_json_fields(limit, dry_run=False)
            else:
                logger.info("操作已取消")
                
        elif choice == '0':
            logger.info("退出程序")
            
        else:
            logger.error("无效的选择")
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        validator.close_database()
    
    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
