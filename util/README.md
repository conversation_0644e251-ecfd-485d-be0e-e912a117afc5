# util 目录

通用工具脚本集合（与具体表弱耦合），用于检查、清洗与辅助修复。执行时日志默认输出到当前工作目录。

- clean_newlines.py（针对 corpus_astro_en）: 去除 `chart_content_json` 换行（保持 JSON 结构）。
- remove_newlines.py（针对 corpus_astro_en）: 简单移除 `chart_content_json` 中的所有换行并校验 JSON。
- validate_json_fields.py（针对 corpus_en）: 扫描并统计 JSON 字段有效性，支持预演与实修（置 NULL）。
- check_specific_record.py（针对 corpus_en）: 查看某条记录各 JSON 字段是否有效并给出预览。

注意：运行这些脚本时，请在仓库根目录执行，或根据需要切换 CWD 以控制日志的输出位置。
