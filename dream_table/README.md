# Dream Table 处理工具

这个文件夹包含处理 `dream` 表（周公解梦）的相关脚本和工具。

## 文件说明

### 1. alter_dream_table.sql
数据库表结构修改脚本，用于：
- 添加 `title_en` 字段（标题英文翻译）
- 添加 `keyword_en` 字段（关键词英文翻译）
- 为新字段创建索引

### 2. translate_dream.py
Python 翻译脚本，用于：
- 连接数据库
- 批量翻译 title 和 keyword 字段
- 更新到对应的英文字段

## 文件列表

### 1. alter_dream_table.sql
数据库表结构修改脚本

### 2. translate_dream.py
主翻译脚本

### 3. check_translation_status.py
翻译状态检查脚本

### 4. test_dream_translation.py
测试脚本

## 使用步骤

### 第一步：修改数据库表结构
```bash
mysql -u username -p database_name < alter_dream_table.sql
```

### 第二步：测试环境
```bash
# 测试数据库连接和翻译API
python test_dream_translation.py
```

### 第三步：检查当前状态
```bash
# 查看翻译进度
python check_translation_status.py
```

### 第四步：运行翻译
```bash
# 翻译所有记录
python translate_dream.py

# 或者测试模式（只翻译少量记录）
# 修改 translate_dream.py 中的 main() 函数，取消注释测试行
```

## 配置说明

所有脚本中的数据库连接参数和API密钥已预配置，如需修改请编辑对应文件中的配置部分。

## 原始表结构
```sql
CREATE TABLE `dream` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `keyword` varchar(50) DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  `sec_type` int(11) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `is_show` int(11) DEFAULT NULL,
  `info` text DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `show` (`is_show`) USING BTREE,
  KEY `keyword` (`keyword`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13863 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='周公解梦';
```

## 修改后的表结构
添加了以下字段：
- `title_en` varchar(200) - 标题英文翻译
- `keyword_en` varchar(100) - 关键词英文翻译
- 相应的索引
