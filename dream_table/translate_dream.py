#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译 dream 表中的 title 和 keyword 字段到对应的英文字段
"""

import os
import mysql.connector
import json
import re
import time
import logging
from typing import Dict, Any, Optional, List
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dream_translation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DreamTranslator:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        # DeepSeek API配置
        self.api_key = '***********************************'
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'
        # API请求参数
        self.request_timeout_seconds = 120
        self.request_max_tokens = 2000
        self.request_temperature = 0.0
        self.retry_sleep_seconds = 2
        
        # 需要翻译的字段映射
        self.translation_fields = {
            'title': 'title_en',
            'keyword': 'keyword_en'
        }
        
        # 连接数据库
        self.connection = None
        self.cursor = None

        # 问题记录文件
        self.error_log_file = 'dream_translation_errors.txt'
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def has_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        if not text:
            return False
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))

    def log_error_to_file(self, record_id: int, field_name: str, error_msg: str, original_text: str = "", translated_text: str = ""):
        """将错误信息记录到文件"""
        try:
            with open(self.error_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n{'='*80}\n")
                f.write(f"时间: {timestamp}\n")
                f.write(f"记录ID: {record_id}\n")
                f.write(f"字段: {field_name}\n")
                f.write(f"错误信息: {error_msg}\n")
                f.write(f"原文: {original_text[:500]}{'...' if len(original_text) > 500 else ''}\n")
                if translated_text:
                    f.write(f"翻译结果: {translated_text[:500]}{'...' if len(translated_text) > 500 else ''}\n")
                f.write(f"{'='*80}\n")
        except Exception as e:
            logger.error(f"写入错误日志文件失败: {e}")
    
    def call_deepseek_api(self, text: str, field_name: str) -> Optional[str]:
        """调用DeepSeek API进行翻译"""
        if not text or not self.has_chinese(text):
            return text
        
        # 根据字段类型选择不同的翻译提示词
        if field_name == 'title':
            prompt = f"""请将以下周公解梦标题翻译成英文。

要求：
1. 使用简洁准确的英文表达
2. 保持梦境解释的专业性
3. 确保翻译后不包含任何中文字符
4. 只返回翻译结果，不要任何解释或额外前后缀

原文：
{text}"""
        
        elif field_name == 'keyword':
            prompt = f"""请将以下周公解梦关键词翻译成英文。

要求：
1. 使用简洁的英文关键词
2. 保持梦境元素的准确性
3. 确保翻译后不包含任何中文字符
4. 只返回翻译结果，不要任何解释或额外前后缀

原文：
{text}"""
        
        else:
            prompt = f"""请将以下中文文本翻译成英文。

要求：
1. 使用准确的英文表达
2. 确保翻译后不包含任何中文字符
3. 只返回翻译结果，不要任何解释或额外前后缀

原文：
{text}"""
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'deepseek-chat',
            'messages': [
                {
                    'role': 'system',
                    'content': '你是一个专业的中译英翻译器，专门翻译周公解梦相关内容。仅返回翻译结果，不添加任何解释、前缀、后缀。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': self.request_temperature,
            'max_tokens': self.request_max_tokens
        }
        
        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=self.request_timeout_seconds)
            response.raise_for_status()
            
            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()
            
            # 清理翻译结果，移除可能的引号
            translated_text = translated_text.strip('"\'')
            
            logger.info(f"翻译成功 - 字段: {field_name}, 原文: {text}, 译文: {translated_text}")
            return translated_text
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"翻译过程出错: {e}")
            return None

    def get_untranslated_records(self, limit: int = None) -> list:
        """获取未翻译的记录"""
        try:
            # 首先检查表结构和数据情况
            logger.info("检查表结构和数据情况...")

            # 检查表是否有新字段
            self.cursor.execute("SHOW COLUMNS FROM dream LIKE 'title_en'")
            title_en_exists = self.cursor.fetchone()

            self.cursor.execute("SHOW COLUMNS FROM dream LIKE 'keyword_en'")
            keyword_en_exists = self.cursor.fetchone()

            if not title_en_exists or not keyword_en_exists:
                logger.error("英文字段不存在，请先执行 alter_dream_table.sql")
                return []

            # 检查总记录数
            self.cursor.execute("SELECT COUNT(*) as total FROM dream")
            total_count = self.cursor.fetchone()['total']
            logger.info(f"表中总记录数: {total_count}")

            # 检查有中文的记录数（使用更简单的中文检测）
            self.cursor.execute("""
                SELECT COUNT(*) as count FROM dream
                WHERE (title IS NOT NULL AND title != '' AND title LIKE '%中%')
                OR (keyword IS NOT NULL AND keyword != '' AND keyword LIKE '%中%')
            """)
            chinese_count = self.cursor.fetchone()['count']
            logger.info(f"包含'中'字的记录数: {chinese_count}")

            # 使用更宽泛的中文检测方式
            self.cursor.execute("""
                SELECT COUNT(*) as count FROM dream
                WHERE (title IS NOT NULL AND title != '' AND CHAR_LENGTH(title) != LENGTH(title))
                OR (keyword IS NOT NULL AND keyword != '' AND CHAR_LENGTH(keyword) != LENGTH(keyword))
            """)
            multibyte_count = self.cursor.fetchone()['count']
            logger.info(f"包含多字节字符的记录数: {multibyte_count}")

            # 查看一些示例数据
            self.cursor.execute("SELECT id, title, keyword, title_en, keyword_en FROM dream LIMIT 5")
            samples = self.cursor.fetchall()
            logger.info("示例数据:")
            for sample in samples:
                logger.info(f"ID: {sample['id']}, title: {sample['title']}, keyword: {sample['keyword']}")
                logger.info(f"  title_en: {sample['title_en']}, keyword_en: {sample['keyword_en']}")

            # 修改查询条件，使用更简单的方式检测中文
            query = """
            SELECT id, title, keyword, title_en, keyword_en
            FROM dream
            WHERE (
                (title IS NOT NULL AND title != '' AND CHAR_LENGTH(title) != LENGTH(title) AND (title_en IS NULL OR title_en = ''))
                OR
                (keyword IS NOT NULL AND keyword != '' AND CHAR_LENGTH(keyword) != LENGTH(keyword) AND (keyword_en IS NULL OR keyword_en = ''))
            )
            ORDER BY id
            """

            if limit:
                query += f" LIMIT {limit}"

            self.cursor.execute(query)
            records = self.cursor.fetchall()

            logger.info(f"找到 {len(records)} 条需要翻译的记录")

            # 如果还是没找到，尝试更宽泛的查询
            if len(records) == 0:
                logger.info("尝试更宽泛的查询...")
                query2 = """
                SELECT id, title, keyword, title_en, keyword_en
                FROM dream
                WHERE (title_en IS NULL OR title_en = '' OR keyword_en IS NULL OR keyword_en = '')
                ORDER BY id
                """

                if limit:
                    query2 += f" LIMIT {limit if limit else 10}"
                else:
                    query2 += " LIMIT 10"

                self.cursor.execute(query2)
                records = self.cursor.fetchall()
                logger.info(f"宽泛查询找到 {len(records)} 条记录")

            return records

        except Exception as e:
            logger.error(f"获取未翻译记录失败: {e}")
            return []

    def translate_record(self, record: Dict[str, Any]) -> bool:
        """翻译单条记录"""
        record_id = record['id']
        logger.info(f"开始翻译记录 ID: {record_id}")

        translated_fields = {}
        success_count = 0
        total_fields = 0

        for source_field, target_field in self.translation_fields.items():
            source_value = record.get(source_field)
            target_value = record.get(target_field)

            # 跳过空值或已翻译的字段
            if not source_value or (target_value and target_value.strip()):
                continue

            # 跳过不包含中文的字段
            if not self.has_chinese(str(source_value)):
                continue

            total_fields += 1
            logger.info(f"翻译字段: {source_field} -> {target_field}")

            # 尝试翻译，最多重试2次
            max_retries = 2
            translated_text = None

            for retry in range(max_retries + 1):
                translated_text = self.call_deepseek_api(str(source_value), source_field)

                if translated_text is not None:
                    # 检查翻译结果是否还包含中文
                    if not self.has_chinese(translated_text):
                        translated_fields[target_field] = translated_text
                        success_count += 1
                        if retry > 0:
                            logger.info(f"字段 {source_field} 重试第{retry}次后翻译成功")
                        else:
                            logger.info(f"字段 {source_field} 翻译完成")
                        break
                    else:
                        if retry < max_retries:
                            logger.warning(f"字段 {source_field} 翻译结果仍包含中文，准备重试第{retry+1}次")
                            time.sleep(self.retry_sleep_seconds)
                            continue
                        else:
                            logger.error(f"字段 {source_field} 重试{max_retries}次后翻译结果仍包含中文")
                            self.log_error_to_file(record_id, source_field, f"翻译结果仍包含中文 (重试{max_retries}次后仍失败)", str(source_value), translated_text)
                            break
                else:
                    if retry < max_retries:
                        logger.warning(f"字段 {source_field} API翻译失败，准备重试第{retry+1}次")
                        time.sleep(self.retry_sleep_seconds)
                        continue
                    else:
                        logger.error(f"字段 {source_field} API翻译重试{max_retries}次后仍失败")
                        self.log_error_to_file(record_id, source_field, f"API翻译失败 (重试{max_retries}次后仍失败)", str(source_value))
                        break

            # 每个字段处理后稍作延迟，避免API限制
            time.sleep(1)
        
        # 更新翻译结果到数据库
        if translated_fields:
            try:
                update_parts = []
                update_values = []

                for field, value in translated_fields.items():
                    update_parts.append(f"{field} = %s")
                    update_values.append(value)

                update_values.append(record_id)
                
                update_query = f"UPDATE dream SET {', '.join(update_parts)} WHERE id = %s"
                self.cursor.execute(update_query, update_values)
                self.connection.commit()
                
                logger.info(f"记录 {record_id} 翻译完成，成功翻译 {success_count}/{total_fields} 个字段")
                return True
                
            except Exception as e:
                logger.error(f"更新记录 {record_id} 失败: {e}")
                self.connection.rollback()
                return False
        else:
            logger.info(f"记录 {record_id} 无需翻译或翻译失败")
            return total_fields == 0  # 如果没有需要翻译的字段，返回True

    def run_translation(self, batch_size: int = 10, max_records: int = None):
        """运行翻译任务"""
        if not self.connect_database():
            return

        try:
            # 获取未翻译的记录
            records = self.get_untranslated_records(max_records)
            
            if not records:
                logger.info("没有需要翻译的记录")
                return

            logger.info(f"开始翻译 {len(records)} 条记录")
            
            success_count = 0
            for i, record in enumerate(records, 1):
                logger.info(f"处理进度: {i}/{len(records)}")
                
                if self.translate_record(record):
                    success_count += 1
                
                # 批次间延迟
                if i % batch_size == 0:
                    logger.info(f"已处理 {i} 条记录，暂停 3 秒...")
                    time.sleep(3)
            
            logger.info(f"翻译任务完成！成功翻译 {success_count}/{len(records)} 条记录")
            
        except Exception as e:
            logger.error(f"翻译任务执行失败: {e}")
        finally:
            self.close_database()

def main():
    """主函数"""
    translator = DreamTranslator()
    
    # 可以通过参数控制翻译数量
    # translator.run_translation(batch_size=10, max_records=100)  # 测试模式：只翻译100条
    translator.run_translation(batch_size=10)  # 翻译所有记录

if __name__ == "__main__":
    main()
