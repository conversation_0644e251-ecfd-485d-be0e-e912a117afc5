#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dream 表翻译功能
"""

import mysql.connector
import logging
from translate_dream import DreamTranslator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    
    db_config = {
        'host': '************',
        'port': 13956,
        'user': 'horoscope_rwu',
        'password': 'h46eaN6JoQxo0VIe',
        'database': 'horoscope_prod',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'dream'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✓ dream 表存在")
            
            # 检查表结构
            cursor.execute("DESCRIBE dream")
            columns = cursor.fetchall()
            
            column_names = [col['Field'] for col in columns]
            print(f"✓ 表字段: {', '.join(column_names)}")
            
            # 检查是否有新字段
            has_title_en = 'title_en' in column_names
            has_keyword_en = 'keyword_en' in column_names
            
            if has_title_en and has_keyword_en:
                print("✓ 英文字段已存在")
            else:
                print("✗ 英文字段不存在，需要先执行 alter_dream_table.sql")
                return False
            
            # 获取一些示例数据
            cursor.execute("SELECT id, title, keyword, title_en, keyword_en FROM dream LIMIT 5")
            samples = cursor.fetchall()
            
            print("\n示例数据:")
            for sample in samples:
                print(f"ID: {sample['id']}")
                print(f"  Title: {sample['title']} -> {sample['title_en']}")
                print(f"  Keyword: {sample['keyword']} -> {sample['keyword_en']}")
                print()
            
        else:
            print("✗ dream 表不存在")
            return False
        
        cursor.close()
        connection.close()
        print("✓ 数据库连接测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_translation_api():
    """测试翻译API"""
    print("\n测试翻译API...")
    
    translator = DreamTranslator()
    
    # 测试翻译功能
    test_cases = [
        ("梦见蛇", "keyword"),
        ("梦见飞翔的感觉", "title"),
        ("水", "keyword"),
        ("梦见自己在考试", "title")
    ]
    
    for text, field_type in test_cases:
        print(f"测试翻译: {text} ({field_type})")
        result = translator.call_deepseek_api(text, field_type)
        if result:
            print(f"  结果: {result}")
        else:
            print(f"  翻译失败")
        print()

def test_small_batch_translation():
    """测试小批量翻译"""
    print("\n测试小批量翻译...")
    
    translator = DreamTranslator()
    
    # 只翻译前5条记录
    translator.run_translation(batch_size=5, max_records=5)

def main():
    """主函数"""
    print("Dream 表翻译功能测试")
    print("="*50)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("数据库连接测试失败，请检查配置")
        return
    
    # 2. 测试翻译API
    try:
        test_translation_api()
    except Exception as e:
        print(f"翻译API测试失败: {e}")
    
    # 3. 询问是否进行小批量翻译测试
    response = input("\n是否进行小批量翻译测试？(y/n): ")
    if response.lower() == 'y':
        try:
            test_small_batch_translation()
        except Exception as e:
            print(f"小批量翻译测试失败: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
