#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库中的 dream 表数据
"""

import mysql.connector
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DreamDebugger:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def has_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        if not text:
            return False
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))

    def debug_table_structure(self):
        """调试表结构"""
        print("\n=== 表结构调试 ===")
        
        # 检查表是否存在
        self.cursor.execute("SHOW TABLES LIKE 'dream'")
        table_exists = self.cursor.fetchone()
        print(f"表是否存在: {bool(table_exists)}")
        
        if not table_exists:
            return False
        
        # 检查表结构
        self.cursor.execute("DESCRIBE dream")
        columns = self.cursor.fetchall()
        
        print("表字段:")
        for col in columns:
            print(f"  {col['Field']}: {col['Type']} - {col['Null']} - {col['Default']}")
        
        # 检查是否有英文字段
        column_names = [col['Field'] for col in columns]
        has_title_en = 'title_en' in column_names
        has_keyword_en = 'keyword_en' in column_names
        
        print(f"title_en 字段存在: {has_title_en}")
        print(f"keyword_en 字段存在: {has_keyword_en}")
        
        return has_title_en and has_keyword_en

    def debug_data_content(self):
        """调试数据内容"""
        print("\n=== 数据内容调试 ===")
        
        # 总记录数
        self.cursor.execute("SELECT COUNT(*) as total FROM dream")
        total_count = self.cursor.fetchone()['total']
        print(f"总记录数: {total_count}")
        
        # 查看前10条记录
        self.cursor.execute("SELECT id, title, keyword, title_en, keyword_en FROM dream ORDER BY id LIMIT 10")
        records = self.cursor.fetchall()
        
        print("\n前10条记录:")
        for record in records:
            print(f"ID: {record['id']}")
            print(f"  title: '{record['title']}' (中文: {self.has_chinese(record['title'] or '')})")
            print(f"  keyword: '{record['keyword']}' (中文: {self.has_chinese(record['keyword'] or '')})")
            print(f"  title_en: '{record['title_en']}'")
            print(f"  keyword_en: '{record['keyword_en']}'")
            print()

    def debug_chinese_detection(self):
        """调试中文检测"""
        print("\n=== 中文检测调试 ===")
        
        # 方法1: 使用正则表达式
        self.cursor.execute("""
            SELECT COUNT(*) as count FROM dream 
            WHERE title REGEXP '[\\u4e00-\\u9fff]' OR keyword REGEXP '[\\u4e00-\\u9fff]'
        """)
        regex_count = self.cursor.fetchone()['count']
        print(f"正则表达式检测到的中文记录数: {regex_count}")
        
        # 方法2: 使用字符长度差异
        self.cursor.execute("""
            SELECT COUNT(*) as count FROM dream 
            WHERE CHAR_LENGTH(title) != LENGTH(title) OR CHAR_LENGTH(keyword) != LENGTH(keyword)
        """)
        length_count = self.cursor.fetchone()['count']
        print(f"字符长度差异检测到的记录数: {length_count}")
        
        # 方法3: 查找包含常见中文字符的记录
        common_chinese = ['梦', '见', '的', '了', '在', '是', '有', '和', '与', '中', '上', '下', '大', '小']
        for char in common_chinese[:5]:  # 只检查前5个
            self.cursor.execute(f"SELECT COUNT(*) as count FROM dream WHERE title LIKE '%{char}%' OR keyword LIKE '%{char}%'")
            char_count = self.cursor.fetchone()['count']
            print(f"包含'{char}'的记录数: {char_count}")

    def debug_translation_status(self):
        """调试翻译状态"""
        print("\n=== 翻译状态调试 ===")
        
        # 检查英文字段的填充情况
        self.cursor.execute("SELECT COUNT(*) as count FROM dream WHERE title_en IS NOT NULL AND title_en != ''")
        title_en_filled = self.cursor.fetchone()['count']
        print(f"title_en 已填充的记录数: {title_en_filled}")
        
        self.cursor.execute("SELECT COUNT(*) as count FROM dream WHERE keyword_en IS NOT NULL AND keyword_en != ''")
        keyword_en_filled = self.cursor.fetchone()['count']
        print(f"keyword_en 已填充的记录数: {keyword_en_filled}")
        
        # 查找需要翻译的记录（使用不同的检测方法）
        methods = [
            ("正则表达式", "title REGEXP '[\\u4e00-\\u9fff]' AND (title_en IS NULL OR title_en = '')"),
            ("字符长度", "CHAR_LENGTH(title) != LENGTH(title) AND (title_en IS NULL OR title_en = '')"),
            ("包含'梦'字", "title LIKE '%梦%' AND (title_en IS NULL OR title_en = '')"),
        ]
        
        for method_name, condition in methods:
            self.cursor.execute(f"SELECT COUNT(*) as count FROM dream WHERE {condition}")
            count = self.cursor.fetchone()['count']
            print(f"{method_name}检测需要翻译的title记录数: {count}")

    def show_sample_untranslated(self):
        """显示需要翻译的示例记录"""
        print("\n=== 需要翻译的示例记录 ===")
        
        # 使用最宽泛的条件查找
        self.cursor.execute("""
            SELECT id, title, keyword, title_en, keyword_en 
            FROM dream 
            WHERE (title_en IS NULL OR title_en = '' OR keyword_en IS NULL OR keyword_en = '')
            AND (title IS NOT NULL AND title != '' OR keyword IS NOT NULL AND keyword != '')
            ORDER BY id 
            LIMIT 10
        """)
        
        records = self.cursor.fetchall()
        print(f"找到 {len(records)} 条可能需要翻译的记录:")
        
        for record in records:
            print(f"\nID: {record['id']}")
            print(f"  title: '{record['title']}' -> '{record['title_en']}'")
            print(f"  keyword: '{record['keyword']}' -> '{record['keyword_en']}'")
            print(f"  title包含中文: {self.has_chinese(record['title'] or '')}")
            print(f"  keyword包含中文: {self.has_chinese(record['keyword'] or '')}")

    def run_debug(self):
        """运行所有调试"""
        if not self.connect_database():
            return
        
        try:
            # 检查表结构
            if not self.debug_table_structure():
                print("表结构有问题，请先执行 alter_dream_table.sql")
                return
            
            # 调试数据内容
            self.debug_data_content()
            
            # 调试中文检测
            self.debug_chinese_detection()
            
            # 调试翻译状态
            self.debug_translation_status()
            
            # 显示示例记录
            self.show_sample_untranslated()
            
        except Exception as e:
            logger.error(f"调试过程出错: {e}")
        finally:
            self.close_database()

def main():
    """主函数"""
    debugger = DreamDebugger()
    debugger.run_debug()

if __name__ == "__main__":
    main()
