-- 为 dream 表添加英文字段
-- 添加 title_en 和 keyword_en 字段

USE your_database_name; -- 请替换为实际的数据库名

-- 添加 title_en 字段
ALTER TABLE `dream` 
ADD COLUMN `title_en` varchar(200) DEFAULT NULL COMMENT '标题英文翻译' AFTER `title`;

-- 添加 keyword_en 字段  
ALTER TABLE `dream` 
ADD COLUMN `keyword_en` varchar(100) DEFAULT NULL COMMENT '关键词英文翻译' AFTER `keyword`;

-- 为新字段添加索引以提高查询性能
ALTER TABLE `dream` 
ADD INDEX `idx_title_en` (`title_en`),
ADD INDEX `idx_keyword_en` (`keyword_en`);

-- 查看修改后的表结构
DESCRIBE `dream`;
