#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 dream 表的翻译状态
"""

import mysql.connector
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DreamTranslationChecker:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def has_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        if not text:
            return False
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))

    def check_translation_status(self):
        """检查翻译状态"""
        if not self.connect_database():
            return

        try:
            # 获取总记录数
            self.cursor.execute("SELECT COUNT(*) as total FROM dream")
            total_records = self.cursor.fetchone()['total']
            
            # 获取有中文title的记录数
            self.cursor.execute("""
                SELECT COUNT(*) as count FROM dream 
                WHERE title IS NOT NULL AND title != '' AND title REGEXP '[\\u4e00-\\u9fff]'
            """)
            chinese_title_count = self.cursor.fetchone()['count']
            
            # 获取有中文keyword的记录数
            self.cursor.execute("""
                SELECT COUNT(*) as count FROM dream 
                WHERE keyword IS NOT NULL AND keyword != '' AND keyword REGEXP '[\\u4e00-\\u9fff]'
            """)
            chinese_keyword_count = self.cursor.fetchone()['count']
            
            # 获取已翻译title的记录数
            self.cursor.execute("""
                SELECT COUNT(*) as count FROM dream 
                WHERE title IS NOT NULL AND title != '' AND title REGEXP '[\\u4e00-\\u9fff]'
                AND title_en IS NOT NULL AND title_en != ''
            """)
            translated_title_count = self.cursor.fetchone()['count']
            
            # 获取已翻译keyword的记录数
            self.cursor.execute("""
                SELECT COUNT(*) as count FROM dream 
                WHERE keyword IS NOT NULL AND keyword != '' AND keyword REGEXP '[\\u4e00-\\u9fff]'
                AND keyword_en IS NOT NULL AND keyword_en != ''
            """)
            translated_keyword_count = self.cursor.fetchone()['count']
            
            # 获取需要翻译但未翻译的记录数
            self.cursor.execute("""
                SELECT COUNT(*) as count FROM dream 
                WHERE (
                    (title IS NOT NULL AND title != '' AND title REGEXP '[\\u4e00-\\u9fff]' AND (title_en IS NULL OR title_en = ''))
                    OR 
                    (keyword IS NOT NULL AND keyword != '' AND keyword REGEXP '[\\u4e00-\\u9fff]' AND (keyword_en IS NULL OR keyword_en = ''))
                )
            """)
            untranslated_count = self.cursor.fetchone()['count']
            
            # 检查英文字段中是否还有中文（翻译质量问题）
            self.cursor.execute("""
                SELECT COUNT(*) as count FROM dream 
                WHERE (title_en IS NOT NULL AND title_en != '' AND title_en REGEXP '[\\u4e00-\\u9fff]')
                OR (keyword_en IS NOT NULL AND keyword_en != '' AND keyword_en REGEXP '[\\u4e00-\\u9fff]')
            """)
            quality_issue_count = self.cursor.fetchone()['count']
            
            # 输出统计结果
            print("\n" + "="*60)
            print("Dream 表翻译状态统计")
            print("="*60)
            print(f"总记录数: {total_records:,}")
            print(f"包含中文title的记录数: {chinese_title_count:,}")
            print(f"包含中文keyword的记录数: {chinese_keyword_count:,}")
            print(f"已翻译title的记录数: {translated_title_count:,}")
            print(f"已翻译keyword的记录数: {translated_keyword_count:,}")
            print(f"仍需翻译的记录数: {untranslated_count:,}")
            print(f"翻译质量问题记录数: {quality_issue_count:,}")
            
            if chinese_title_count > 0:
                title_progress = (translated_title_count / chinese_title_count) * 100
                print(f"Title翻译进度: {title_progress:.1f}%")
            
            if chinese_keyword_count > 0:
                keyword_progress = (translated_keyword_count / chinese_keyword_count) * 100
                print(f"Keyword翻译进度: {keyword_progress:.1f}%")
            
            print("="*60)
            
            # 如果有翻译质量问题，显示一些示例
            if quality_issue_count > 0:
                print("\n翻译质量问题示例:")
                self.cursor.execute("""
                    SELECT id, title, title_en, keyword, keyword_en FROM dream 
                    WHERE (title_en IS NOT NULL AND title_en != '' AND title_en REGEXP '[\\u4e00-\\u9fff]')
                    OR (keyword_en IS NOT NULL AND keyword_en != '' AND keyword_en REGEXP '[\\u4e00-\\u9fff]')
                    LIMIT 5
                """)
                quality_issues = self.cursor.fetchall()
                
                for issue in quality_issues:
                    print(f"ID: {issue['id']}")
                    if issue['title_en'] and self.has_chinese(issue['title_en']):
                        print(f"  Title问题: {issue['title']} -> {issue['title_en']}")
                    if issue['keyword_en'] and self.has_chinese(issue['keyword_en']):
                        print(f"  Keyword问题: {issue['keyword']} -> {issue['keyword_en']}")
                    print()
            
            # 显示一些翻译示例
            print("\n翻译示例:")
            self.cursor.execute("""
                SELECT id, title, title_en, keyword, keyword_en FROM dream 
                WHERE title_en IS NOT NULL AND title_en != ''
                AND keyword_en IS NOT NULL AND keyword_en != ''
                LIMIT 5
            """)
            examples = self.cursor.fetchall()
            
            for example in examples:
                print(f"ID: {example['id']}")
                print(f"  Title: {example['title']} -> {example['title_en']}")
                print(f"  Keyword: {example['keyword']} -> {example['keyword_en']}")
                print()
                
        except Exception as e:
            logger.error(f"检查翻译状态失败: {e}")
        finally:
            self.close_database()

def main():
    """主函数"""
    checker = DreamTranslationChecker()
    checker.check_translation_status()

if __name__ == "__main__":
    main()
