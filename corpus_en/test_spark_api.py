#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试星火大模型API调用
"""

import requests
import json

def test_spark_api():
    """测试星火大模型API调用"""
    
    # API配置
    api_url = 'https://spark-api-open.xf-yun.com/v1/chat/completions'
    api_password = 'JFuxlHZGMFkKuAInuxAY:GamczgXrAtXiQjnWGYIo'
    
    # 构建请求头
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_password}'
    }
    
    # 构建请求体
    data = {
        "model": "generalv3.5",  # 使用Max版本
        "messages": [
            {"role": "user", "content": "你好，请说一句话"}
        ],
        "temperature": 0.1,
        "max_tokens": 100,
        "stream": False
    }
    
    try:
        print("发送API请求...")
        print(f"URL: {api_url}")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(api_url, headers=headers, json=data, timeout=30)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('code') == 0:
                choices = result.get('choices', [])
                if choices:
                    content = choices[0].get('message', {}).get('content', '')
                    print(f"翻译结果: {content}")
                    return True
                else:
                    print("响应中没有choices")
                    return False
            else:
                print(f"API返回错误: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"请求失败: {e}")
        return False

if __name__ == "__main__":
    test_spark_api()
