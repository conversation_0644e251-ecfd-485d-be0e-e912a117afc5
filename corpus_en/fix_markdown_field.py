#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门修复 chart_content_markdown 字段的无效JSON问题
将包含Markdown内容的字段设置为null
"""

import mysql.connector
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_markdown_field.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def connect_database():
    """连接数据库"""
    db_config = {
        'host': '************',
        'port': 13956,
        'user': 'horoscope_rwu',
        'password': 'h46eaN6JoQxo0VIe',
        'database': 'horoscope_prod',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        logger.info("数据库连接成功")
        return connection, cursor
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None, None

def is_valid_json(text):
    """检查文本是否为有效的JSON格式"""
    if not text or text.strip() == '':
        return True  # 空值或空字符串认为是有效的
    
    try:
        json.loads(text)
        return True
    except (json.JSONDecodeError, TypeError, ValueError):
        return False

def preview_invalid_markdown_records(cursor, limit=10):
    """预览包含无效JSON的chart_content_markdown记录"""
    try:
        logger.info(f"预览前 {limit} 条包含无效JSON的 chart_content_markdown 记录...")
        
        # 获取非空的chart_content_markdown记录
        cursor.execute("""
            SELECT id, chart_content_markdown 
            FROM corpus_en 
            WHERE chart_content_markdown IS NOT NULL 
            AND chart_content_markdown != '' 
            ORDER BY id 
            LIMIT %s
        """, (limit,))
        
        records = cursor.fetchall()
        
        invalid_count = 0
        for record in records:
            record_id = record['id']
            field_value = record['chart_content_markdown']
            
            if not is_valid_json(field_value):
                invalid_count += 1
                preview_text = str(field_value)[:200] + ('...' if len(str(field_value)) > 200 else '')
                logger.info(f"ID {record_id}: {preview_text}")
                logger.info("-" * 80)
        
        logger.info(f"在前 {len(records)} 条记录中发现 {invalid_count} 条无效JSON记录")
        
    except Exception as e:
        logger.error(f"预览失败: {e}")

def get_invalid_markdown_count(cursor):
    """获取无效JSON的chart_content_markdown记录总数"""
    try:
        # 获取所有非空的chart_content_markdown记录
        cursor.execute("""
            SELECT COUNT(*) as total 
            FROM corpus_en 
            WHERE chart_content_markdown IS NOT NULL 
            AND chart_content_markdown != ''
        """)
        
        total_non_empty = cursor.fetchone()['total']
        logger.info(f"chart_content_markdown 非空记录总数: {total_non_empty}")
        
        # 检查所有记录的JSON有效性
        cursor.execute("""
            SELECT id, chart_content_markdown 
            FROM corpus_en 
            WHERE chart_content_markdown IS NOT NULL 
            AND chart_content_markdown != ''
        """)
        
        records = cursor.fetchall()
        invalid_count = 0
        
        for record in records:
            if not is_valid_json(record['chart_content_markdown']):
                invalid_count += 1
        
        logger.info(f"无效JSON记录数: {invalid_count}")
        return invalid_count, total_non_empty
        
    except Exception as e:
        logger.error(f"统计失败: {e}")
        return 0, 0

def fix_invalid_markdown_records(cursor, connection, dry_run=True):
    """修复无效的chart_content_markdown记录"""
    try:
        logger.info("开始修复 chart_content_markdown 字段的无效JSON...")
        
        if dry_run:
            logger.info("=== 预演模式 (不会实际修改数据) ===")
        else:
            logger.info("=== 实际修复模式 ===")
        
        # 获取所有非空的chart_content_markdown记录
        cursor.execute("""
            SELECT id, chart_content_markdown 
            FROM corpus_en 
            WHERE chart_content_markdown IS NOT NULL 
            AND chart_content_markdown != ''
            ORDER BY id
        """)
        
        records = cursor.fetchall()
        logger.info(f"找到 {len(records)} 条非空记录")
        
        invalid_records = []
        for record in records:
            if not is_valid_json(record['chart_content_markdown']):
                invalid_records.append(record['id'])
        
        logger.info(f"其中 {len(invalid_records)} 条记录包含无效JSON")
        
        if not invalid_records:
            logger.info("没有需要修复的记录")
            return
        
        if dry_run:
            logger.info(f"[预演] 将修复以下记录的 chart_content_markdown 字段:")
            for i, record_id in enumerate(invalid_records[:10]):  # 只显示前10个
                logger.info(f"  ID: {record_id}")
            if len(invalid_records) > 10:
                logger.info(f"  ... 还有 {len(invalid_records) - 10} 条记录")
        else:
            # 实际修复
            batch_size = 50
            success_count = 0
            
            for i in range(0, len(invalid_records), batch_size):
                batch = invalid_records[i:i + batch_size]
                
                # 构建批量更新SQL
                placeholders = ','.join(['%s'] * len(batch))
                update_query = f"""
                UPDATE corpus_en 
                SET chart_content_markdown = NULL 
                WHERE id IN ({placeholders})
                """
                
                cursor.execute(update_query, batch)
                connection.commit()
                
                success_count += len(batch)
                logger.info(f"已修复 {success_count}/{len(invalid_records)} 条记录")
            
            logger.info(f"修复完成！共修复 {success_count} 条记录")
        
    except Exception as e:
        logger.error(f"修复失败: {e}")
        if connection and not dry_run:
            connection.rollback()

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("修复 chart_content_markdown 字段的无效JSON问题")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)
    
    connection, cursor = connect_database()
    if not connection:
        return
    
    try:
        print("\n请选择操作:")
        print("1. 预览包含无效JSON的记录")
        print("2. 统计无效记录数量")
        print("3. 预演修复操作")
        print("4. 实际执行修复")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            limit = input("输入预览记录数 (默认10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            preview_invalid_markdown_records(cursor, limit)
            
        elif choice == '2':
            get_invalid_markdown_count(cursor)
            
        elif choice == '3':
            fix_invalid_markdown_records(cursor, connection, dry_run=True)
            
        elif choice == '4':
            print(f"\n⚠️  警告：这将修改数据库，将无效JSON的 chart_content_markdown 字段设置为NULL")
            confirm = input("确认继续？(y/n): ").strip().lower()
            
            if confirm in ['y', 'yes', '是']:
                fix_invalid_markdown_records(cursor, connection, dry_run=False)
            else:
                logger.info("操作已取消")
                
        elif choice == '0':
            logger.info("退出程序")
            
        else:
            logger.error("无效的选择")
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("数据库连接已关闭")
    
    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
