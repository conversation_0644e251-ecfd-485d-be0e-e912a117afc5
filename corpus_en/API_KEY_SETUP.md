# 星火大模型API密钥配置说明

## 问题描述
当前代码出现 `AppIdNoAuthError` 错误（错误码11200），这表示API认证失败。

## 解决方案

### 1. 获取正确的APIPassword

1. 访问讯飞开放平台控制台：https://console.xfyun.cn/services/bm35
2. 登录您的账号
3. 找到对应的星火大模型应用
4. 在"HTTP服务接口认证信息"区域找到 `APIPassword`
5. 复制这个APIPassword（不是appid，也不是api_secret）

### 2. 更新代码中的API密钥

在 `translate_corpus_en.py` 文件中，找到第50行左右的配置：

```python
# 星火大模型API配置 - 使用HTTP方式
self.api_url = 'https://spark-api-open.xf-yun.com/v1/chat/completions'
self.appid = '82ab5727'
# 注意：这里需要的是从讯飞开放平台控制台获取的APIPassword，不是appid和api_password的组合
# 请到 https://console.xfyun.cn/services/bm35 获取正确的APIPassword
self.api_password = '这里替换为您从控制台获取的APIPassword'
```

将 `self.api_password` 的值替换为您从控制台获取的正确APIPassword。

### 3. 验证API密钥

运行测试脚本验证API密钥是否正确：

```bash
python3 test_spark_api.py
```

如果配置正确，应该能看到成功的响应。

### 4. 重新运行翻译程序

```bash
python3 translate_corpus_en.py
```

## 注意事项

1. **APIPassword格式**：应该是一个完整的字符串，不是appid和api_secret的组合
2. **权限检查**：确保您的账号有足够的调用额度
3. **模型版本**：当前使用的是 `generalv3.5`（Max版本），确保您的账号有该版本的使用权限

## 错误码说明

- `11200`: 授权错误，该appId没有相关功能的授权或业务量超过限制
- `11201`: 日流控超限，超过当日最大访问量的限制
- `11202`: 秒级流控超限，秒级并发超过授权路数限制
- `11203`: 并发流控超限，并发路数超过授权路数限制

如果遇到其他错误码，请参考讯飞开放平台的官方文档。
