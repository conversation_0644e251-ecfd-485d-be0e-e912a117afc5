#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速检查 corpus_en 表中JSON字段的有效性
"""

import mysql.connector
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def connect_database():
    """连接数据库"""
    db_config = {
        'host': '************',
        'port': 13956,
        'user': 'horoscope_rwu',
        'password': 'h46eaN6JoQxo0VIe',
        'database': 'horoscope_prod',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        logger.info("数据库连接成功")
        return connection, cursor
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None, None

def is_valid_json(text):
    """检查文本是否为有效的JSON格式"""
    if not text or text.strip() == '':
        return True  # 空值或空字符串认为是有效的
    
    try:
        json.loads(text)
        return True
    except (json.JSONDecodeError, TypeError, ValueError):
        return False

def quick_check():
    """快速检查JSON字段"""
    connection, cursor = connect_database()
    if not connection:
        return
    
    try:
        # JSON字段列表
        json_fields = [
            'new_content',
            'transit_chart_content',
            'combination_chart_content',
            'thirdprogressed_chart_content',
            'secondarylimit_chart_content',
            'lunarreturn_chart_content',
            'solarreturn_chart_content',
            'solararc_chart_content',
            'developed_chart_content',
            'smalllimit_chart_content',
            'nataltwelvepointer_chart_content',
            'natalthirteenpointer_chart_content',
            'current_chart_content',
            'chart_content_markdown',
            'comparision_a_chart_content',
            'comparision_b_chart_content',
            'compositeThirprogr_chart_content',
            'compositesecondary_chart_content',
            'marks_a_chart_content',
            'marks_b_chart_content',
            'marksthirprogr_a_chart_content',
            'marksthirprogr_b_chart_content',
            'markssecprogr_a_chart_content',
            'markssecprogr_b_chart_content',
            'timesmidpoint_chart_content',
            'timesmidpointthirprogr_chart_content',
            'timesmidpointsecprogr_chart_content'
        ]
        
        logger.info("开始快速检查JSON字段...")
        
        # 获取总记录数
        cursor.execute("SELECT COUNT(*) as total FROM corpus_en")
        total_records = cursor.fetchone()['total']
        logger.info(f"总记录数: {total_records}")
        
        # 逐个字段检查
        field_stats = {}
        
        for field in json_fields:
            logger.info(f"检查字段: {field}")
            
            # 获取非空记录数
            cursor.execute(f"SELECT COUNT(*) as count FROM corpus_en WHERE {field} IS NOT NULL AND {field} != ''")
            non_empty_count = cursor.fetchone()['count']
            
            # 快速检查前100条非空记录
            cursor.execute(f"SELECT id, {field} FROM corpus_en WHERE {field} IS NOT NULL AND {field} != '' LIMIT 100")
            sample_records = cursor.fetchall()
            
            valid_count = 0
            invalid_count = 0
            invalid_examples = []
            
            for record in sample_records:
                field_value = record[field]
                if is_valid_json(field_value):
                    valid_count += 1
                else:
                    invalid_count += 1
                    if len(invalid_examples) < 3:  # 只保存前3个例子
                        preview = str(field_value)[:100] + ('...' if len(str(field_value)) > 100 else '')
                        invalid_examples.append(f"ID {record['id']}: {preview}")
            
            field_stats[field] = {
                'non_empty_total': non_empty_count,
                'sample_size': len(sample_records),
                'valid_in_sample': valid_count,
                'invalid_in_sample': invalid_count,
                'invalid_examples': invalid_examples
            }
            
            if invalid_count > 0:
                logger.info(f"  ❌ 发现无效JSON: {invalid_count}/{len(sample_records)} (样本)")
                for example in invalid_examples:
                    logger.info(f"    {example}")
            else:
                logger.info(f"  ✅ 样本中所有记录都是有效JSON")
        
        # 输出汇总
        logger.info("\n=== 检查结果汇总 ===")
        total_invalid_fields = 0
        
        for field, stats in field_stats.items():
            if stats['invalid_in_sample'] > 0:
                total_invalid_fields += 1
                estimated_invalid = int((stats['invalid_in_sample'] / stats['sample_size']) * stats['non_empty_total'])
                logger.info(f"{field}: 预估有 {estimated_invalid} 条无效记录 (基于样本 {stats['invalid_in_sample']}/{stats['sample_size']})")
        
        if total_invalid_fields == 0:
            logger.info("🎉 所有字段的样本检查都通过了！")
        else:
            logger.info(f"⚠️  发现 {total_invalid_fields} 个字段可能包含无效JSON")
            logger.info("建议运行完整的修复脚本进行处理")
        
    except Exception as e:
        logger.error(f"检查过程出错: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("数据库连接已关闭")

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("快速检查 corpus_en 表中JSON字段的有效性")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)
    
    quick_check()
    
    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
