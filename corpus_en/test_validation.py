#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻译结果验证功能
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from translate_corpus_en import CorpusEnTranslator

def test_validation():
    """测试验证功能"""
    translator = CorpusEnTranslator()
    
    print("测试JSON验证功能:")
    
    # 测试有效JSON
    valid_json = '{"name": "<PERSON>", "age": 30}'
    is_valid, error = translator.validate_json(valid_json)
    print(f"有效JSON: {is_valid}, 错误: {error}")
    
    # 测试无效JSON
    invalid_json = '{"name": "<PERSON>", "age": 30'
    is_valid, error = translator.validate_json(invalid_json)
    print(f"无效JSON: {is_valid}, 错误: {error}")
    
    # 测试包含中文的JSON
    chinese_json = '{"name": "张三", "age": 30}'
    is_valid, error = translator.validate_translation_result(chinese_json, 'new_content')
    print(f"包含中文的JSON: {is_valid}, 错误: {error}")
    
    print("\n测试Markdown验证功能:")
    
    # 测试有效Markdown
    valid_markdown = "# Hello World\n\nThis is a **bold** text."
    is_valid, error = translator.validate_markdown(valid_markdown)
    print(f"有效Markdown: {is_valid}, 错误: {error}")
    
    # 测试包含中文的Markdown
    chinese_markdown = "# 你好世界\n\n这是一个**粗体**文本。"
    is_valid, error = translator.validate_translation_result(chinese_markdown, 'chart_content_markdown')
    print(f"包含中文的Markdown: {is_valid}, 错误: {error}")
    
    print("\n测试HTML验证功能:")
    
    # 测试有效HTML
    valid_html = "<p>Hello <strong>World</strong></p>"
    is_valid, error = translator.validate_html(valid_html)
    print(f"有效HTML: {is_valid}, 错误: {error}")
    
    # 测试包含中文的HTML
    chinese_html = "<p>你好 <strong>世界</strong></p>"
    is_valid, error = translator.validate_translation_result(chinese_html, 'content')
    print(f"包含中文的HTML: {is_valid}, 错误: {error}")
    
    print("\n测试错误日志功能:")
    
    # 测试错误日志
    translator.log_error_to_file(
        record_id=123,
        field_name="test_field",
        error_msg="测试错误信息",
        original_text="原始中文内容",
        translated_text="翻译后内容"
    )
    print("错误日志已写入 translation_errors.txt")

if __name__ == "__main__":
    test_validation()
