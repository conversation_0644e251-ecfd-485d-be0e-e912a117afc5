#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查翻译状态的辅助脚本
"""

import mysql.connector
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TranslationStatusChecker:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        self.connection = None
        self.cursor = None
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def check_translation_status(self):
        """检查翻译状态统计"""
        if not self.connect_database():
            return
        
        try:
            # 检查翻译状态统计
            self.cursor.execute("""
                SELECT 
                    translation_status,
                    COUNT(*) as count
                FROM corpus_en 
                WHERE translation_status IS NOT NULL
                GROUP BY translation_status
                ORDER BY count DESC
            """)
            
            status_stats = self.cursor.fetchall()
            
            print("翻译状态统计:")
            print("-" * 40)
            for stat in status_stats:
                print(f"{stat['translation_status']}: {stat['count']} 条")
            
            # 检查仍包含中文的记录
            fields_to_check = [
                'content', 'new_content', 'transit_chart_content', 'combination_chart_content',
                'thirdprogressed_chart_content', 'secondarylimit_chart_content', 
                'lunarreturn_chart_content', 'solarreturn_chart_content', 'solararc_chart_content',
                'developed_chart_content', 'smalllimit_chart_content', 'nataltwelvepointer_chart_content',
                'natalthirteenpointer_chart_content', 'current_chart_content', 'chart_content_markdown',
                'comparision_a_chart_content', 'comparision_b_chart_content', 'compositeThirprogr_chart_content',
                'compositesecondary_chart_content', 'marks_a_chart_content', 'marks_b_chart_content',
                'marksthirprogr_a_chart_content', 'marksthirprogr_b_chart_content', 'markssecprogr_a_chart_content',
                'markssecprogr_b_chart_content', 'timesmidpoint_chart_content', 'timesmidpointthirprogr_chart_content',
                'timesmidpointsecprogr_chart_content', 'title'
            ]
            
            chinese_conditions = []
            for field in fields_to_check:
                chinese_conditions.append(f"{field} REGEXP '[\\u4e00-\\u9fff]'")
            
            where_clause = " OR ".join(chinese_conditions)
            
            self.cursor.execute(f"""
                SELECT COUNT(*) as count
                FROM corpus_en 
                WHERE {where_clause}
            """)
            
            chinese_count = self.cursor.fetchone()['count']
            print(f"\n仍包含中文的记录: {chinese_count} 条")
            
            # 检查最近的翻译活动
            self.cursor.execute("""
                SELECT 
                    DATE(translation_updated_at) as date,
                    COUNT(*) as count
                FROM corpus_en 
                WHERE translation_updated_at IS NOT NULL
                GROUP BY DATE(translation_updated_at)
                ORDER BY date DESC
                LIMIT 7
            """)
            
            recent_activity = self.cursor.fetchall()
            
            print("\n最近7天的翻译活动:")
            print("-" * 40)
            for activity in recent_activity:
                print(f"{activity['date']}: {activity['count']} 条")
            
        except Exception as e:
            logger.error(f"检查翻译状态失败: {e}")
        finally:
            self.close_database()
    
    def show_failed_records(self, limit=10):
        """显示失败的记录详情"""
        if not self.connect_database():
            return
        
        try:
            self.cursor.execute("""
                SELECT id, translation_status, translation_progress, translation_updated_at
                FROM corpus_en 
                WHERE translation_status IN ('failed', 'partial_completed')
                ORDER BY translation_updated_at DESC
                LIMIT %s
            """, (limit,))
            
            failed_records = self.cursor.fetchall()
            
            print(f"\n最近{limit}条失败/部分成功的记录:")
            print("-" * 80)
            for record in failed_records:
                print(f"ID: {record['id']}")
                print(f"状态: {record['translation_status']}")
                print(f"进度: {record['translation_progress']}")
                print(f"更新时间: {record['translation_updated_at']}")
                print("-" * 40)
            
        except Exception as e:
            logger.error(f"查询失败记录失败: {e}")
        finally:
            self.close_database()

def main():
    """主函数"""
    checker = TranslationStatusChecker()
    
    print("=" * 60)
    print("翻译状态检查报告")
    print(f"检查时间: {datetime.now()}")
    print("=" * 60)
    
    checker.check_translation_status()
    checker.show_failed_records()
    
    print("=" * 60)

if __name__ == "__main__":
    main()
