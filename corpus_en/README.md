# corpus_en 目录

包含针对 `corpus_en` 表的翻译与数据修复脚本、日志。

- translate_corpus_en.py: 翻译 `corpus_en` 多字段（HTML/JSON/Markdown/文本），带状态列与错误日志。
- get_full_content.py: 查看指定记录与字段的完整内容（含 JSON 检查）。
- fix_all_invalid_json.py: 扫描并修复全表无效 JSON 字段（可预演/实修）。
- fix_json_fields.py / fix_markdown_field.py / quick_json_check.py: JSON/Markdown 质量检查与修复辅助。
- check_translation_status.py: 查看失败/部分成功记录与统计。
- translate_corpus_en.log: 执行日志。
- translation_errors.txt: 错误明细。
