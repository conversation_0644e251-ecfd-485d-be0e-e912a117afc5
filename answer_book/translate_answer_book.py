#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将 `answer_book` 表的 `answer` 字段翻译为英文，写入 `answer_en` 字段。

使用 DeepSeek Chat API 进行翻译。默认仅处理 `answer_en` 为空且 `answer` 含中文的记录。

用法示例：
  python answer_book/translate_answer_book.py --limit 200 --sleep 0.5
"""

from __future__ import annotations

import argparse
import logging
import re
import time
from typing import List, Dict, Any, Optional

import mysql.connector
import requests


# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('answer_book_translation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AnswerBookTranslator:
    """将 `answer_book.answer` 翻译为英文并写入 `answer_en`。"""

    def __init__(self) -> None:
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4',
        }

        self.api_key = 'sk-d4ef9b9a7206493186144fa1f36f686f'
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'

        self.connection: Optional[mysql.connector.MySQLConnection] = None
        self.cursor: Optional[mysql.connector.cursor.MySQLCursorDict] = None

    # -------------------- DB --------------------
    def connect_database(self) -> bool:
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            # type: ignore[attr-defined]
            self.cursor = self.connection.cursor(dictionary=True)  # type: ignore[assignment]
            logger.info('数据库连接成功')
            return True
        except Exception as exc:  # noqa: BLE001
            logger.error(f'数据库连接失败: {exc}')
            return False

    def close_database(self) -> None:
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info('数据库连接已关闭')

    def ensure_answer_en_column(self) -> None:
        """确保 `answer_en` 字段存在；若不存在则添加。"""
        assert self.cursor is not None and self.connection is not None
        try:
            self.cursor.execute(
                """
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'answer_book' AND COLUMN_NAME = 'answer_en'
                """,
                (self.db_config['database'],),
            )
            exists = self.cursor.fetchone() is not None
            if not exists:
                logger.info('未检测到 answer_en 字段，正在新增...')
                self.cursor.execute(
                    """
                    ALTER TABLE answer_book
                    ADD COLUMN answer_en TEXT NULL
                    """
                )
                self.connection.commit()
                logger.info('已添加 answer_en 字段')
        except Exception as exc:  # noqa: BLE001
            logger.error(f'检查/添加 answer_en 字段失败: {exc}')
            raise

    # -------------------- Utils --------------------
    @staticmethod
    def has_chinese(text: str | None) -> bool:
        if not text:
            return False
        return bool(re.search(r'[\u4e00-\u9fff]+', text))

    def call_deepseek_translation(self, text: str) -> Optional[str]:
        if not text or not self.has_chinese(text):
            return text

        prompt = (
            '将以下中文内容准确翻译为自然流畅的英文，仅返回译文本身，不要任何解释或标注：\n\n' + text
        )

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
        }
        data = {
            'model': 'deepseek-chat',
            'messages': [
                {
                    'role': 'user',
                    'content': prompt,
                }
            ],
            'temperature': 0.3,
            'max_tokens': 3000,
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            response.raise_for_status()
            result = response.json()
            translated = result['choices'][0]['message']['content'].strip()
            logger.info('翻译成功 | 源长度=%d, 译文长度=%d', len(text), len(translated))
            return translated
        except requests.exceptions.RequestException as exc:
            logger.error(f'API请求失败: {exc}')
            return None
        except Exception as exc:  # noqa: BLE001
            logger.error(f'翻译异常: {exc}')
            return None

    # -------------------- Query/Update --------------------
    def fetch_pending_records(self, limit: int, start_id: Optional[int], require_chinese: bool) -> List[Dict[str, Any]]:
        assert self.cursor is not None
        params: list[Any] = []
        where_clauses = [
            "(answer_en IS NULL OR TRIM(answer_en) = '')",
            "answer IS NOT NULL",
            "TRIM(answer) <> ''",
        ]
        if require_chinese:
            where_clauses.append("answer REGEXP '[\\u4e00-\\u9fff]'")
        if start_id is not None:
            where_clauses.append('id >= %s')
            params.append(start_id)

        sql = (
            'SELECT id, answer '\
            'FROM answer_book '\
            f"WHERE {' AND '.join(where_clauses)} "
            'ORDER BY id '
            'LIMIT %s'
        )
        params.append(limit)

        self.cursor.execute(sql, tuple(params))
        rows = self.cursor.fetchall()
        logger.info('待处理记录：%d', len(rows))
        return rows

    def update_answer_en(self, record_id: int, translated: str) -> None:
        assert self.cursor is not None and self.connection is not None
        self.cursor.execute(
            'UPDATE answer_book SET answer_en = %s WHERE id = %s',
            (translated, record_id),
        )
        self.connection.commit()

    # -------------------- Orchestration --------------------
    def run(self, limit: int, sleep_seconds: float, start_id: Optional[int], require_chinese: bool) -> None:
        if not self.connect_database():
            return
        try:
            self.ensure_answer_en_column()
            records = self.fetch_pending_records(limit=limit, start_id=start_id, require_chinese=require_chinese)
            if not records:
                logger.info('没有需要处理的记录')
                return

            success = 0
            failed = 0
            for idx, row in enumerate(records, start=1):
                record_id = row['id']
                source = str(row['answer']) if row['answer'] is not None else ''
                logger.info('(%d/%d) 处理记录 id=%s', idx, len(records), record_id)

                if not source:
                    logger.info('记录 id=%s 无内容，跳过', record_id)
                    continue

                translated = self.call_deepseek_translation(source)
                if translated is None:
                    failed += 1
                    logger.error('记录 id=%s 翻译失败，跳过', record_id)
                    continue

                try:
                    self.update_answer_en(record_id, translated)
                    success += 1
                    logger.info('记录 id=%s 已更新 answer_en', record_id)
                finally:
                    if sleep_seconds > 0:
                        time.sleep(sleep_seconds)

            logger.info('处理完成 | 成功=%d, 失败=%d', success, failed)
        finally:
            self.close_database()


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description='翻译 answer_book.answer 至 answer_book.answer_en')
    parser.add_argument('--limit', type=int, default=200, help='本次最多处理的记录数')
    parser.add_argument('--sleep', type=float, default=1.0, help='每条之间的休眠秒数，防止触发限流')
    parser.add_argument('--start-id', type=int, default=None, help='从指定 id 开始处理')
    parser.add_argument('--no-chinese-filter', action='store_true',
                        help='不过滤中文，处理所有 answer_en 为空的记录（若原文非中文则直接拷贝到 answer_en）')
    return parser.parse_args()


def main() -> None:
    args = parse_args()
    translator = AnswerBookTranslator()
    translator.run(limit=args.limit,
                   sleep_seconds=args.sleep,
                   start_id=args.start_id,
                   require_chinese=(not args.no_chinese_filter))


if __name__ == '__main__':
    main()


