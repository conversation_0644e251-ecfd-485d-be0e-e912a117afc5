#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为 `personality_test_questions` 表新增英文字段并将中文翻译为英文：
  - 新增字段：`question_text_en`、`option_text_en`
  - 将 `question_text`、`option_text` 翻译为英文写入对应 *_en 字段

示例运行：
  python personality_test_questions/translate_personality_test_questions.py --limit 200 --sleep 1.0
  python personality_test_questions/translate_personality_test_questions.py --limit 200 --sleep 1.0 --no-chinese-filter
"""

from __future__ import annotations

import argparse
import logging
import re
import time
from typing import Any, Dict, List, Optional

import mysql.connector
import requests


# 日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ptq_translation.log', encoding='utf-8'),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


class PTQTranslator:
    """翻译 personality_test_questions.question_text / option_text 到 *_en 字段"""

    def __init__(self) -> None:
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4',
        }

        self.api_key = 'sk-d4ef9b9a7206493186144fa1f36f686f'
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'

        self.connection: Optional[mysql.connector.MySQLConnection] = None
        self.cursor: Optional[mysql.connector.cursor.MySQLCursorDict] = None

    # ---------------- DB ----------------
    def connect_database(self) -> bool:
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            # type: ignore[attr-defined]
            self.cursor = self.connection.cursor(dictionary=True)  # type: ignore[assignment]
            logger.info('数据库连接成功')
            return True
        except Exception as exc:  # noqa: BLE001
            logger.error(f'数据库连接失败: {exc}')
            return False

    def close_database(self) -> None:
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info('数据库连接已关闭')

    def ensure_translation_columns(self) -> None:
        """确保 question_text_en、option_text_en 字段存在，不存在则添加。"""
        assert self.cursor is not None and self.connection is not None
        try:
            self.cursor.execute(
                """
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'personality_test_questions'
                  AND COLUMN_NAME IN ('question_text_en', 'option_text_en')
                """,
                (self.db_config['database'],),
            )
            existing = {row['COLUMN_NAME'] for row in self.cursor.fetchall()}

            if 'question_text_en' not in existing:
                logger.info('未检测到 question_text_en 字段，正在新增...')
                self.cursor.execute(
                    """
                    ALTER TABLE personality_test_questions
                    ADD COLUMN question_text_en TEXT NULL
                    """
                )
            if 'option_text_en' not in existing:
                logger.info('未检测到 option_text_en 字段，正在新增...')
                self.cursor.execute(
                    """
                    ALTER TABLE personality_test_questions
                    ADD COLUMN option_text_en TEXT NULL
                    """
                )
            if 'question_text_en' not in existing or 'option_text_en' not in existing:
                self.connection.commit()
                logger.info('新增字段完成')
        except Exception as exc:  # noqa: BLE001
            logger.error(f'检查/添加英文字段失败: {exc}')
            raise

    # --------------- Utils ---------------
    @staticmethod
    def has_chinese(text: Optional[str]) -> bool:
        if not text:
            return False
        return bool(re.search(r'[\u4e00-\u9fff]+', text))

    def translate_text(self, text: str) -> Optional[str]:
        if not text:
            return text
        if not self.has_chinese(text):
            # 非中文不调用翻译，直接返回原文（在关闭中文过滤时会使用）
            return text

        prompt = (
            '将以下中文内容准确翻译为自然流畅的英文，仅返回译文本身，不要任何解释或标注：\n\n' + text
        )
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
        }
        data = {
            'model': 'deepseek-chat',
            'messages': [
                {'role': 'user', 'content': prompt},
            ],
            'temperature': 0.3,
            'max_tokens': 1500,
        }
        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            response.raise_for_status()
            result = response.json()
            translated = result['choices'][0]['message']['content'].strip()
            logger.info('翻译成功 | 源长=%d, 译长=%d', len(text), len(translated))
            return translated
        except requests.exceptions.RequestException as exc:
            logger.error(f'API请求失败: {exc}')
            return None
        except Exception as exc:  # noqa: BLE001
            logger.error(f'翻译异常: {exc}')
            return None

    # --------------- Query/Update ---------------
    def fetch_pending(self, limit: int, start_id: Optional[int], require_chinese: bool) -> List[Dict[str, Any]]:
        assert self.cursor is not None
        where_parts: List[str] = [
            "((question_text_en IS NULL OR TRIM(question_text_en) = '') OR (option_text_en IS NULL OR TRIM(option_text_en) = ''))",
            "((question_text IS NOT NULL AND TRIM(question_text) <> '') OR (option_text IS NOT NULL AND TRIM(option_text) <> ''))",
        ]
        params: List[Any] = []
        if require_chinese:
            where_parts.append(
                "(question_text REGEXP '[\\u4e00-\\u9fff]' OR option_text REGEXP '[\\u4e00-\\u9fff]')"
            )
        if start_id is not None:
            where_parts.append('id >= %s')
            params.append(start_id)

        sql = (
            'SELECT id, question_text, option_text, question_text_en, option_text_en '
            'FROM personality_test_questions '
            f"WHERE {' AND '.join(where_parts)} "
            'ORDER BY id '
            'LIMIT %s'
        )
        params.append(limit)
        self.cursor.execute(sql, tuple(params))
        rows = self.cursor.fetchall()
        logger.info('待处理记录：%d', len(rows))
        return rows

    def update_row(self, row_id: int, q_en: Optional[str], o_en: Optional[str]) -> None:
        assert self.cursor is not None and self.connection is not None
        sets: List[str] = []
        vals: List[Any] = []
        if q_en is not None:
            sets.append('question_text_en = %s')
            vals.append(q_en)
        if o_en is not None:
            sets.append('option_text_en = %s')
            vals.append(o_en)
        if not sets:
            return
        vals.append(row_id)
        sql = f"UPDATE personality_test_questions SET {', '.join(sets)} WHERE id = %s"
        self.cursor.execute(sql, tuple(vals))
        self.connection.commit()

    # --------------- Orchestration ---------------
    def run(self, limit: int, sleep_seconds: float, start_id: Optional[int], require_chinese: bool) -> None:
        if not self.connect_database():
            return
        try:
            self.ensure_translation_columns()
            rows = self.fetch_pending(limit=limit, start_id=start_id, require_chinese=require_chinese)
            if not rows:
                logger.info('没有需要处理的记录')
                return

            success = 0
            failed = 0
            for idx, row in enumerate(rows, start=1):
                row_id = row['id']
                logger.info('(%d/%d) 处理记录 id=%s', idx, len(rows), row_id)

                q_src: Optional[str] = row.get('question_text')
                o_src: Optional[str] = row.get('option_text')

                q_need = (row.get('question_text_en') is None or str(row.get('question_text_en')).strip() == '') and q_src and str(q_src).strip() != ''
                o_need = (row.get('option_text_en') is None or str(row.get('option_text_en')).strip() == '') and o_src and str(o_src).strip() != ''

                q_en: Optional[str] = None
                o_en: Optional[str] = None

                try:
                    if q_need:
                        if require_chinese and not self.has_chinese(q_src):
                            logger.info('question_text 非中文，跳过翻译')
                            q_en = None
                        else:
                            q_en = self.translate_text(str(q_src))
                    if o_need:
                        if require_chinese and not self.has_chinese(o_src):
                            logger.info('option_text 非中文，跳过翻译')
                            o_en = None
                        else:
                            o_en = self.translate_text(str(o_src))

                    if q_en is None and o_en is None:
                        logger.info('无可更新字段，跳过')
                    else:
                        self.update_row(row_id, q_en, o_en)
                        success += 1
                        logger.info('记录 id=%s 已更新 *_en 字段', row_id)
                except Exception as exc:  # noqa: BLE001
                    failed += 1
                    logger.error(f'记录 id={row_id} 处理失败: {exc}')
                finally:
                    if sleep_seconds > 0:
                        time.sleep(sleep_seconds)

            logger.info('处理完成 | 成功=%d, 失败=%d', success, failed)
        finally:
            self.close_database()


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description='翻译 personality_test_questions.* 到英文字段')
    parser.add_argument('--limit', type=int, default=200, help='本次最多处理的记录数')
    parser.add_argument('--sleep', type=float, default=1.0, help='每条之间的休眠秒数')
    parser.add_argument('--start-id', type=int, default=None, help='从指定 id 开始处理')
    parser.add_argument('--no-chinese-filter', action='store_true', help='不过滤中文，所有空的 *_en 字段都会被填充（非中文原文将被直接拷贝）')
    return parser.parse_args()


def main() -> None:
    args = parse_args()
    translator = PTQTranslator()
    translator.run(
        limit=args.limit,
        sleep_seconds=args.sleep,
        start_id=args.start_id,
        require_chinese=(not args.no_chinese_filter),
    )


if __name__ == '__main__':
    main()


