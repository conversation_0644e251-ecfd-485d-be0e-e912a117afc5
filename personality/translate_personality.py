#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为 `personality` 表新增英文字段并翻译：
  - 处理字段：type_name, type_title, type_desc(保留换行), tag_name, description(富文本)
  - 新增对应字段：type_name_en, type_title_en, type_desc_en, tag_name_en, description_en

运行示例：
  python personality/translate_personality.py --limit 100 --sleep 1.0
  python personality/translate_personality.py --limit 100 --sleep 1.0 --no-chinese-filter
"""

from __future__ import annotations

import argparse
import logging
import re
import time
from typing import Any, Dict, List, Optional

import mysql.connector
import requests


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('personality_translation.log', encoding='utf-8'),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


class PersonalityTranslator:
    """翻译 personality 表的中文内容到对应英文列。"""

    def __init__(self) -> None:
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4',
        }

        self.api_key = 'sk-d4ef9b9a7206493186144fa1f36f686f'
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'

        self.connection: Optional[mysql.connector.MySQLConnection] = None
        self.cursor: Optional[mysql.connector.cursor.MySQLCursorDict] = None

        self.text_fields = ['type_name', 'type_title', 'type_desc', 'tag_name', 'description']
        self.field_to_en = {
            'type_name': 'type_name_en',
            'type_title': 'type_title_en',
            'type_desc': 'type_desc_en',
            'tag_name': 'tag_name_en',
            'description': 'description_en',
        }

    # --------------- DB ---------------
    def connect_database(self) -> bool:
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            # type: ignore[attr-defined]
            self.cursor = self.connection.cursor(dictionary=True)  # type: ignore[assignment]
            logger.info('数据库连接成功')
            return True
        except Exception as exc:  # noqa: BLE001
            logger.error(f'数据库连接失败: {exc}')
            return False

    def close_database(self) -> None:
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info('数据库连接已关闭')

    def ensure_translation_columns(self) -> None:
        """确保 *_en 字段存在，不存在则添加。"""
        assert self.cursor is not None and self.connection is not None
        try:
            self.cursor.execute(
                """
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'personality'
                """,
                (self.db_config['database'],),
            )
            existing = {row['COLUMN_NAME'] for row in self.cursor.fetchall()}

            alter_needed = []
            # 选择合理的列类型：短文本用 VARCHAR(255)，长文本用 TEXT
            if 'type_name_en' not in existing:
                alter_needed.append("ADD COLUMN type_name_en VARCHAR(255) NULL")
            if 'type_title_en' not in existing:
                alter_needed.append("ADD COLUMN type_title_en VARCHAR(255) NULL")
            if 'type_desc_en' not in existing:
                alter_needed.append("ADD COLUMN type_desc_en TEXT NULL")
            if 'tag_name_en' not in existing:
                alter_needed.append("ADD COLUMN tag_name_en VARCHAR(255) NULL")
            if 'description_en' not in existing:
                alter_needed.append("ADD COLUMN description_en TEXT NULL")

            if alter_needed:
                sql = "ALTER TABLE personality " + ", ".join(alter_needed)
                logger.info('新增英文字段: %s', ', '.join([p.split()[2] for p in alter_needed]))
                self.cursor.execute(sql)
                self.connection.commit()
                logger.info('新增字段完成')
        except Exception as exc:  # noqa: BLE001
            logger.error(f'检查/添加英文字段失败: {exc}')
            raise

    # --------------- Utils ---------------
    @staticmethod
    def has_chinese(text: Optional[str]) -> bool:
        if not text:
            return False
        return bool(re.search(r'[\u4e00-\u9fff]+', text))

    @staticmethod
    def looks_like_html(text: str) -> bool:
        if not text:
            return False
        # 粗略判断是否包含 HTML 标签
        return bool(re.search(r'<\s*\w+[^>]*>', text))

    @staticmethod
    def looks_like_markdown(text: str) -> bool:
        if not text:
            return False
        # 粗略判断 Markdown 特征
        md_patterns = [r'^\s*#', r'\*\*.+\*\*', r'\*\s', r'`{1,3}[^`]+`{1,3}', r'\[.+\]\(.+\)']
        return any(re.search(p, text, flags=re.MULTILINE) for p in md_patterns)

    @staticmethod
    def clean_fence_markers(text: str) -> str:
        if not text:
            return text
        # 移除模型可能返回的代码块围栏
        text = re.sub(r'^```(html|markdown|md|text)?\s*\n?', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\n?```\s*$', '', text)
        return text.strip()

    def translate_field_value(self, field_name: str, text: str) -> Optional[str]:
        if not text:
            return text

        # 构建针对不同字段的提示词
        if field_name == 'description':
            # 富文本：保持 HTML/Markdown 结构，仅翻译文本
            if self.looks_like_html(text):
                prompt = (
                    '将以下 HTML 富文本中的中文翻译成英文，严格保持所有 HTML 标签与属性不变，仅翻译标签内的可见文本；'
                    '直接返回译文，不要任何解释、不要代码块标记：\n\n' + text
                )
            elif self.looks_like_markdown(text):
                prompt = (
                    '将以下 Markdown 内容中的中文翻译成英文，严格保持 Markdown 语法与结构不变；'
                    '直接返回译文，不要任何解释、不要代码块标记：\n\n' + text
                )
            else:
                prompt = (
                    '将以下富文本/长文本中的中文翻译成英文，保持段落结构与空行不变；'
                    '直接返回译文，不要任何解释、不要代码块标记：\n\n' + text
                )
        elif field_name == 'type_desc':
            # 保留换行
            prompt = (
                '将以下文本翻译为英文，严格保持原有换行与空白行的结构；'
                '直接返回译文，不要任何解释、不要代码块标记：\n\n' + text
            )
        else:
            # 短文本
            prompt = (
                '将以下中文翻译为准确自然的英文，仅返回译文本身，不要任何解释或标注：\n\n' + text
            )

        # 针对不同字段设定合适的超时与token上限
        if field_name == 'description':
            field_timeout = 180
            max_tokens = 5000
        elif field_name == 'type_desc':
            field_timeout = 90
            max_tokens = 3500
        else:
            field_timeout = 60
            max_tokens = 3000

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
        }
        data = {
            'model': 'deepseek-chat',
            'messages': [
                {'role': 'user', 'content': prompt},
            ],
            'temperature': 0.3,
            'max_tokens': max_tokens,
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=field_timeout)
            response.raise_for_status()
            result = response.json()
            translated = result['choices'][0]['message']['content'].strip()
            translated = self.clean_fence_markers(translated)
            logger.info('翻译成功 | 字段=%s 源长=%d, 译长=%d', field_name, len(text), len(translated))
            return translated
        except requests.exceptions.RequestException as exc:
            logger.error(f'API请求失败: {exc}')
            return None

    def translate_with_retries(self, field_name: str, text: str, retries: int = 2, delay_seconds: float = 2.0) -> Optional[str]:
        """为单字段增加简单重试，缓解偶发 API 超时。"""
        # 根据字段动态调整重试策略
        if field_name == 'description':
            retries = max(retries, 3)
            delay_seconds = max(delay_seconds, 3.0)
        elif field_name == 'type_desc':
            retries = max(retries, 2)
            delay_seconds = max(delay_seconds, 2.0)

        attempt = 0
        while attempt <= retries:
            result = self.translate_field_value(field_name, text)
            if result is not None:
                return result
            attempt += 1
            if attempt <= retries:
                logger.info('字段 %s 第 %d 次重试...', field_name, attempt)
                time.sleep(delay_seconds)
        return None

    # --------------- Query/Update ---------------
    def fetch_pending(self, limit: int, start_id: Optional[int], require_chinese: bool) -> List[Dict[str, Any]]:
        assert self.cursor is not None
        en_fields = [self.field_to_en[f] for f in self.text_fields]

        where_parts: List[str] = []
        # 至少有一个英文目标为空，且源字段有值
        empties = [f"({en} IS NULL OR TRIM({en}) = '')" for en in en_fields]
        sources = [
            "(type_name IS NOT NULL AND TRIM(type_name) <> '')",
            "(type_title IS NOT NULL AND TRIM(type_title) <> '')",
            "(type_desc IS NOT NULL AND TRIM(type_desc) <> '')",
            "(tag_name IS NOT NULL AND TRIM(tag_name) <> '')",
            "(description IS NOT NULL AND TRIM(description) <> '')",
        ]
        where_parts.append('(' + ' OR '.join(empties) + ')')
        where_parts.append('(' + ' OR '.join(sources) + ')')

        if require_chinese:
            where_parts.append(
                "(type_name REGEXP '[\\u4e00-\\u9fff]' OR type_title REGEXP '[\\u4e00-\\u9fff]' "
                "OR type_desc REGEXP '[\\u4e00-\\u9fff]' OR tag_name REGEXP '[\\u4e00-\\u9fff]' "
                "OR description REGEXP '[\\u4e00-\\u9fff]')"
            )

        params: List[Any] = []
        if start_id is not None:
            where_parts.append('id >= %s')
            params.append(start_id)

        sql = (
            'SELECT id, type_code, type_name, type_title, type_desc, tag_name, description, '
            'type_name_en, type_title_en, type_desc_en, tag_name_en, description_en '
            'FROM personality '
            f"WHERE {' AND '.join(where_parts)} "
            'ORDER BY id '
            'LIMIT %s'
        )
        params.append(limit)

        self.cursor.execute(sql, tuple(params))
        rows = self.cursor.fetchall()
        logger.info('待处理记录：%d', len(rows))
        return rows

    def update_row(self, row_id: int, updates: Dict[str, str]) -> None:
        assert self.cursor is not None and self.connection is not None
        if not updates:
            return
        sets = [f"{col} = %s" for col in updates.keys()]
        vals: List[Any] = list(updates.values()) + [row_id]
        sql = f"UPDATE personality SET {', '.join(sets)} WHERE id = %s"
        self.cursor.execute(sql, tuple(vals))
        self.connection.commit()

    # --------------- Orchestration ---------------
    def run(self, limit: int, sleep_seconds: float, start_id: Optional[int], require_chinese: bool) -> None:
        if not self.connect_database():
            return
        try:
            self.ensure_translation_columns()
            rows = self.fetch_pending(limit=limit, start_id=start_id, require_chinese=require_chinese)
            if not rows:
                logger.info('没有需要处理的记录')
                return

            success = 0
            failed = 0
            for idx, row in enumerate(rows, start=1):
                row_id = row['id']
                logger.info('(%d/%d) 处理记录 id=%s', idx, len(rows), row_id)

                updates: Dict[str, str] = {}
                # 遍历每个需要处理的字段
                for field in self.text_fields:
                    src_val = row.get(field)
                    dst_col = self.field_to_en[field]
                    dst_val = row.get(dst_col)

                    if src_val is None or str(src_val).strip() == '':
                        continue
                    if dst_val is not None and str(dst_val).strip() != '':
                        continue

                    text = str(src_val)
                    # 在 require_chinese 模式下，如果不含中文则跳过；否则调用翻译
                    if require_chinese and not self.has_chinese(text):
                        continue

                    translated = self.translate_with_retries(field, text)
                    if translated is None:
                        failed += 1
                        logger.error('字段 %s 翻译失败，记录 id=%s', field, row_id)
                        continue

                    updates[dst_col] = translated

                if updates:
                    try:
                        self.update_row(row_id, updates)
                        success += 1
                        logger.info('记录 id=%s 已更新英文字段：%s', row_id, ', '.join(updates.keys()))
                    except Exception as exc:  # noqa: BLE001
                        failed += 1
                        logger.error('记录 id=%s 保存失败: %s', row_id, exc)

                if sleep_seconds > 0:
                    time.sleep(sleep_seconds)

            logger.info('处理完成 | 成功=%d, 失败=%d', success, failed)
        finally:
            self.close_database()


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description='翻译 personality 表到英文列')
    parser.add_argument('--limit', type=int, default=200, help='本次最多处理的记录数')
    parser.add_argument('--sleep', type=float, default=1.0, help='每条之间的休眠秒数')
    parser.add_argument('--start-id', type=int, default=None, help='从指定 id 开始处理')
    parser.add_argument('--no-chinese-filter', action='store_true', help='不过滤中文，所有空的英文列都会被填充（非中文原文将被直接拷贝）')
    return parser.parse_args()


def main() -> None:
    args = parse_args()
    translator = PersonalityTranslator()
    translator.run(
        limit=args.limit,
        sleep_seconds=args.sleep,
        start_id=args.start_id,
        require_chinese=(not args.no_chinese_filter),
    )


if __name__ == '__main__':
    main()


