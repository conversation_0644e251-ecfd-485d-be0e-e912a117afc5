#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译 corpus_en 表中从 content 字段开始的所有字段
"""

import mysql.connector
import json
import re
import time
import logging
from typing import Dict, Any, Optional, List
import requests
from datetime import datetime
import markdown
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translate_corpus_en.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CorpusEnTranslator:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        # DeepSeek API配置
        self.api_key = '***********************************'
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'
        
        # 需要翻译的字段（从content开始）
        self.fields_to_translate = [
            'content',  # 富文本
            'new_content',  # JSON
            'transit_chart_content',  # JSON
            'combination_chart_content',  # JSON
            'thirdprogressed_chart_content',  # JSON
            'secondarylimit_chart_content',  # JSON
            'lunarreturn_chart_content',  # JSON
            'solarreturn_chart_content',  # JSON
            'solararc_chart_content',  # JSON
            'developed_chart_content',  # JSON
            'smalllimit_chart_content',  # JSON
            'nataltwelvepointer_chart_content',  # JSON
            'natalthirteenpointer_chart_content',  # JSON
            'current_chart_content',  # JSON
            'chart_content_markdown',  # Markdown
            'comparision_a_chart_content',  # JSON
            'comparision_b_chart_content',  # JSON
            'compositeThirprogr_chart_content',  # JSON
            'compositesecondary_chart_content',  # JSON
            'marks_a_chart_content',  # JSON
            'marks_b_chart_content',  # JSON
            'marksthirprogr_a_chart_content',  # JSON
            'marksthirprogr_b_chart_content',  # JSON
            'markssecprogr_a_chart_content',  # JSON
            'markssecprogr_b_chart_content',  # JSON
            'timesmidpoint_chart_content',  # JSON
            'timesmidpointthirprogr_chart_content',  # JSON
            'timesmidpointsecprogr_chart_content',  # JSON
            'title'  # 普通文本
        ]
        
        # 富文本字段
        self.rich_text_fields = ['content']
        
        # Markdown字段
        self.markdown_fields = ['chart_content_markdown']
        
        # 普通文本字段
        self.plain_text_fields = ['title']
        
        # JSON字段（其他所有content字段）
        self.json_fields = [field for field in self.fields_to_translate 
                           if field not in self.rich_text_fields + self.markdown_fields + self.plain_text_fields]
        
        # 连接数据库
        self.connection = None
        self.cursor = None

        # 问题记录文件
        self.error_log_file = 'translation_errors.txt'
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def has_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        if not text:
            return False
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))

    def validate_json(self, text: str) -> tuple[bool, str]:
        """验证JSON格式是否正确"""
        if not text:
            return True, ""

        try:
            json.loads(text)
            return True, ""
        except json.JSONDecodeError as e:
            return False, f"JSON格式错误: {str(e)}"

    def validate_markdown(self, text: str) -> tuple[bool, str]:
        """验证Markdown格式是否正确"""
        if not text:
            return True, ""

        try:
            # 尝试解析Markdown
            html = markdown.markdown(text)
            # 检查是否包含中文
            if self.has_chinese(text):
                return False, "Markdown内容仍包含中文"
            return True, ""
        except Exception as e:
            return False, f"Markdown格式错误: {str(e)}"

    def validate_html(self, text: str) -> tuple[bool, str]:
        """验证HTML格式是否正确"""
        if not text:
            return True, ""

        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(text, 'html.parser')
            # 检查是否包含中文
            if self.has_chinese(text):
                return False, "HTML内容仍包含中文"
            return True, ""
        except Exception as e:
            return False, f"HTML格式错误: {str(e)}"

    def validate_translation_result(self, text: str, field_name: str) -> tuple[bool, str]:
        """验证翻译结果的格式和内容"""
        if not text:
            return True, ""

        # 检查是否仍包含中文
        if self.has_chinese(text):
            return False, "翻译结果仍包含中文字符"

        # 根据字段类型进行格式验证
        if field_name in self.json_fields:
            return self.validate_json(text)
        elif field_name in self.markdown_fields:
            return self.validate_markdown(text)
        elif field_name in self.rich_text_fields:
            return self.validate_html(text)
        else:
            # 普通文本字段，只检查是否包含中文
            return True, ""

    def log_error_to_file(self, record_id: int, field_name: str, error_msg: str, original_text: str = "", translated_text: str = ""):
        """将错误信息记录到文件"""
        try:
            with open(self.error_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n{'='*80}\n")
                f.write(f"时间: {timestamp}\n")
                f.write(f"记录ID: {record_id}\n")
                f.write(f"字段名: {field_name}\n")
                f.write(f"错误信息: {error_msg}\n")
                if original_text:
                    f.write(f"原文内容: {original_text[:500]}{'...' if len(original_text) > 500 else ''}\n")
                if translated_text:
                    f.write(f"翻译结果: {translated_text[:500]}{'...' if len(translated_text) > 500 else ''}\n")
                f.write(f"{'='*80}\n")
        except Exception as e:
            logger.error(f"写入错误日志文件失败: {e}")
    
    def call_deepseek_api(self, text: str, field_name: str) -> Optional[str]:
        """调用DeepSeek API进行翻译"""
        if not text or not self.has_chinese(text):
            return text
        
        # 根据字段类型选择不同的翻译提示词
        if field_name in self.rich_text_fields:
            prompt = f"""请将以下HTML富文本内容中的所有中文翻译成英文。

要求：
1. 保持HTML标签和属性完全不变
2. 只翻译标签内的中文文本内容
3. 占星学术语使用专业英文词汇
4. 确保翻译后不包含任何中文字符
5. 直接返回翻译后的HTML内容，不要任何解释或代码块标记

原文：
{text}"""

        elif field_name in self.json_fields:
            prompt = f"""请将以下JSON内容中的所有中文翻译成英文。

要求：
1. 保持JSON结构和格式完全不变
2. 只翻译JSON值中的中文部分，不翻译键名
3. 占星学术语使用专业英文词汇
4. 确保翻译后不包含任何中文字符
5. 返回有效的JSON格式，不要```json标记或任何解释

原文：
{text}"""

        elif field_name in self.markdown_fields:
            prompt = f"""请将以下Markdown内容中的所有中文翻译成英文。

要求：
1. 保持Markdown格式标记完全不变
2. 只翻译文本内容，不翻译Markdown语法
3. 占星学术语使用专业英文词汇
4. 确保翻译后不包含任何中文字符
5. 直接返回翻译后的Markdown内容，不要```markdown标记或任何解释

原文：
{text}"""

        else:  # 普通文本字段
            prompt = f"""请将以下中文文本翻译成英文。

要求：
1. 占星学术语使用专业英文词汇
2. 确保翻译后不包含任何中文字符
3. 只返回翻译结果，不要任何解释

原文：
{text}"""
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'deepseek-chat',
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.3,
            'max_tokens': 4000
        }
        
        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()
            
            # 清理翻译结果
            translated_text = self.clean_translation_result(translated_text, field_name)

            logger.info(f"翻译成功 - 字段: {field_name}, 原文长度: {len(text)}, 译文长度: {len(translated_text)}")
            return translated_text
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"翻译过程出错: {e}")
            return None
    
    def clean_translation_result(self, text: str, field_name: str) -> str:
        """清理翻译结果，移除不必要的标记"""
        if not text:
            return text
        
        # 移除代码块标记
        if field_name in self.json_fields:
            # 移除 ```json 和 ``` 标记
            text = re.sub(r'^```json\s*\n?', '', text, flags=re.IGNORECASE)
            text = re.sub(r'\n?```\s*$', '', text)
        elif field_name in self.markdown_fields:
            # 移除 ```markdown 和 ``` 标记
            text = re.sub(r'^```markdown\s*\n?', '', text, flags=re.IGNORECASE)
            text = re.sub(r'\n?```\s*$', '', text)
        
        return text.strip()
    
    def get_untranslated_records(self, limit: int = None) -> list:
        """获取未翻译的记录"""
        try:
            # 构建查询条件，检查所有需要翻译的字段是否包含中文
            chinese_conditions = []
            for field in self.fields_to_translate:
                chinese_conditions.append(f"{field} REGEXP '[\\u4e00-\\u9fff]'")
            
            where_clause = " OR ".join(chinese_conditions)
            
            query = f"""
            SELECT id, {', '.join(self.fields_to_translate)}
            FROM corpus_en 
            WHERE {where_clause}
            ORDER BY id
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            self.cursor.execute(query)
            records = self.cursor.fetchall()
            logger.info(f"找到 {len(records)} 条需要翻译的记录")
            return records
            
        except Exception as e:
            logger.error(f"查询未翻译记录失败: {e}")
            return []
    
    def add_translation_status_columns(self):
        """添加翻译状态跟踪字段"""
        try:
            # 检查字段是否已存在
            self.cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'corpus_en' 
                AND COLUMN_NAME IN ('translation_status', 'translation_progress', 'translation_updated_at')
            """, (self.db_config['database'],))
            
            existing_columns = [row['COLUMN_NAME'] for row in self.cursor.fetchall()]
            
            if 'translation_status' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE corpus_en 
                    ADD COLUMN translation_status VARCHAR(20) DEFAULT NULL
                """)
                logger.info("添加 translation_status 字段")
            
            if 'translation_progress' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE corpus_en 
                    ADD COLUMN translation_progress TEXT DEFAULT NULL
                """)
                logger.info("添加 translation_progress 字段")
            
            if 'translation_updated_at' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE corpus_en 
                    ADD COLUMN translation_updated_at TIMESTAMP DEFAULT NULL
                """)
                logger.info("添加 translation_updated_at 字段")
            
            self.connection.commit()
            
        except Exception as e:
            logger.error(f"添加翻译状态字段失败: {e}")
    
    def update_translation_status(self, record_id: int, status: str, progress: str = None):
        """更新翻译状态"""
        try:
            if progress:
                query = """
                UPDATE corpus_en 
                SET translation_status = %s, translation_progress = %s, 
                    translation_updated_at = NOW()
                WHERE id = %s
                """
                self.cursor.execute(query, (status, progress, record_id))
            else:
                query = """
                UPDATE corpus_en 
                SET translation_status = %s, translation_updated_at = NOW()
                WHERE id = %s
                """
                self.cursor.execute(query, (status, record_id))
            
            self.connection.commit()
            
        except Exception as e:
            logger.error(f"更新翻译状态失败: {e}")
    
    def translate_record(self, record: Dict[str, Any]) -> bool:
        """翻译单条记录"""
        record_id = record['id']
        logger.info(f"开始翻译记录 ID: {record_id}")

        # 标记为处理中
        self.update_translation_status(record_id, 'processing')

        translated_fields = {}
        progress_info = []
        validation_errors = []

        for field in self.fields_to_translate:
            field_value = record.get(field)

            if not field_value:
                progress_info.append(f"{field}:empty")
                continue

            if not self.has_chinese(str(field_value)):
                progress_info.append(f"{field}:no_chinese")
                continue

            logger.info(f"翻译字段: {field} (类型: {'富文本' if field in self.rich_text_fields else 'JSON' if field in self.json_fields else 'Markdown' if field in self.markdown_fields else '普通文本'})")

            # 尝试翻译，最多重试2次
            max_retries = 2
            translated_text = None

            for retry in range(max_retries + 1):
                translated_text = self.call_deepseek_api(str(field_value), field)

                if translated_text is not None:
                    # 验证翻译结果
                    is_valid, error_msg = self.validate_translation_result(translated_text, field)

                    if is_valid:
                        translated_fields[field] = translated_text
                        progress_info.append(f"{field}:translated")
                        if retry > 0:
                            logger.info(f"字段 {field} 重试第{retry}次后翻译成功并通过验证")
                        else:
                            logger.info(f"字段 {field} 翻译完成并通过验证")
                        break
                    else:
                        if retry < max_retries:
                            logger.warning(f"字段 {field} 翻译结果验证失败: {error_msg}，准备重试第{retry+1}次")
                            time.sleep(2)  # 重试前等待更长时间
                            continue
                        else:
                            progress_info.append(f"{field}:validation_failed_after_retries")
                            validation_errors.append(f"{field}: {error_msg} (重试{max_retries}次后仍失败)")
                            logger.error(f"字段 {field} 重试{max_retries}次后仍验证失败: {error_msg}")

                            # 记录到错误文件
                            self.log_error_to_file(record_id, field, f"{error_msg} (重试{max_retries}次后仍失败)", str(field_value), translated_text)
                            break
                else:
                    if retry < max_retries:
                        logger.warning(f"字段 {field} API翻译失败，准备重试第{retry+1}次")
                        time.sleep(2)
                        continue
                    else:
                        progress_info.append(f"{field}:api_failed_after_retries")
                        logger.error(f"字段 {field} API翻译重试{max_retries}次后仍失败")
                        self.log_error_to_file(record_id, field, f"API翻译失败 (重试{max_retries}次后仍失败)", str(field_value))
                        break

            # 每个字段处理后稍作延迟，避免API限制
            time.sleep(1)
        
        # 更新翻译结果到数据库
        if translated_fields:
            try:
                update_parts = []
                update_values = []

                for field, translated_text in translated_fields.items():
                    update_parts.append(f"{field} = %s")
                    update_values.append(translated_text)

                update_values.append(record_id)

                query = f"""
                UPDATE corpus_en
                SET {', '.join(update_parts)}, translation_updated_at = NOW()
                WHERE id = %s
                """

                self.cursor.execute(query, update_values)
                self.connection.commit()

                # 确定最终状态
                if validation_errors:
                    # 有验证错误，标记为部分完成
                    progress_str = ','.join(progress_info) + f" | 验证错误: {'; '.join(validation_errors)}"
                    self.update_translation_status(record_id, 'partial_completed', progress_str)
                    logger.warning(f"记录 {record_id} 部分翻译完成，存在验证错误")
                    return False
                else:
                    # 全部成功
                    progress_str = ','.join(progress_info)
                    self.update_translation_status(record_id, 'completed', progress_str)
                    logger.info(f"记录 {record_id} 翻译完成并保存")
                    return True

            except Exception as e:
                logger.error(f"保存翻译结果失败: {e}")
                self.update_translation_status(record_id, 'failed', f"save_error:{str(e)}")
                self.log_error_to_file(record_id, "database", f"保存失败: {str(e)}")
                return False
        else:
            # 没有需要翻译的内容或全部翻译失败
            if validation_errors:
                progress_str = ','.join(progress_info) + f" | 验证错误: {'; '.join(validation_errors)}"
                self.update_translation_status(record_id, 'failed', progress_str)
                logger.error(f"记录 {record_id} 翻译失败，存在验证错误")
                return False
            else:
                progress_str = ','.join(progress_info)
                self.update_translation_status(record_id, 'completed', progress_str)
                logger.info(f"记录 {record_id} 无需翻译")
                return True
    
    def preview_records(self, limit: int = 5):
        """预览需要翻译的记录"""
        try:
            records = self.get_untranslated_records(limit)
            
            if not records:
                logger.info("没有找到需要翻译的记录")
                return
            
            logger.info(f"预览前 {len(records)} 条需要翻译的记录：")
            
            for record in records:
                record_id = record['id']
                chinese_fields = []
                
                for field in self.fields_to_translate:
                    field_value = record.get(field)
                    if field_value and self.has_chinese(str(field_value)):
                        field_type = '富文本' if field in self.rich_text_fields else 'JSON' if field in self.json_fields else 'Markdown' if field in self.markdown_fields else '普通文本'
                        chinese_fields.append(f"{field}({field_type})")
                
                logger.info(f"ID: {record_id}, 包含中文的字段: {', '.join(chinese_fields)}")
                
        except Exception as e:
            logger.error(f"预览记录失败: {e}")
    
    def run_translation(self, limit: int = None):
        """运行翻译任务"""
        if not self.connect_database():
            return

        try:
            # 初始化错误日志文件
            with open(self.error_log_file, 'w', encoding='utf-8') as f:
                f.write(f"翻译错误日志 - 开始时间: {datetime.now()}\n")
                f.write("="*80 + "\n")

            # 添加翻译状态跟踪字段
            self.add_translation_status_columns()

            # 预览需要翻译的记录
            self.preview_records()

            # 询问用户是否继续
            print(f"\n是否继续执行翻译操作？(y/n): ", end="")
            user_input = input().strip().lower()

            if user_input not in ['y', 'yes', '是', 'Y']:
                logger.info("用户取消操作")
                return

            # 获取需要翻译的记录
            records = self.get_untranslated_records(limit)

            if not records:
                logger.info("没有需要翻译的记录")
                return

            logger.info(f"开始翻译 {len(records)} 条记录")
            logger.info(f"错误日志将记录到: {self.error_log_file}")

            success_count = 0
            failed_count = 0
            partial_count = 0

            for i, record in enumerate(records, 1):
                logger.info(f"处理进度: {i}/{len(records)}")

                try:
                    result = self.translate_record(record)
                    if result:
                        success_count += 1
                    else:
                        # 检查是否是部分完成
                        self.cursor.execute(
                            "SELECT translation_status FROM corpus_en WHERE id = %s",
                            (record['id'],)
                        )
                        status_result = self.cursor.fetchone()
                        if status_result and status_result['translation_status'] == 'partial_completed':
                            partial_count += 1
                        else:
                            failed_count += 1

                    # 每处理5条记录后稍作休息
                    if i % 5 == 0:
                        logger.info(f"已处理 {i} 条记录，休息3秒...")
                        time.sleep(3)

                except Exception as e:
                    logger.error(f"处理记录 {record['id']} 时出错: {e}")
                    self.log_error_to_file(record['id'], "system", f"系统错误: {str(e)}")
                    failed_count += 1
                    continue

            logger.info(f"翻译任务完成！")
            logger.info(f"完全成功: {success_count}")
            logger.info(f"部分成功: {partial_count}")
            logger.info(f"失败: {failed_count}")
            logger.info(f"错误详情请查看: {self.error_log_file}")

        except Exception as e:
            logger.error(f"翻译任务执行失败: {e}")
        finally:
            self.close_database()

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("开始翻译 corpus_en 表中从 content 字段开始的所有字段")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)
    
    translator = CorpusEnTranslator()
    
    # 可以设置限制处理的记录数，用于测试
    # translator.run_translation(limit=10)  # 只处理前10条记录
    translator.run_translation()  # 处理所有记录
    
    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()